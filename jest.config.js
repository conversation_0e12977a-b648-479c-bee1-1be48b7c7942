module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    roots: ['<rootDir>/test'],
    testMatch: ['**/*.spec.ts'],
    moduleFileExtensions: ['ts', 'js', 'json'],
    transform: {
        '^.+\\.ts$': 'ts-jest',
    },
    moduleDirectories: ['node_modules', 'src', 'test'],
    testTimeout: 20000, // Tăng timeout toàn cục
    setupFilesAfterEnv: ['<rootDir>/test/setup.ts'],
    forceExit: true, // Buộc Jest thoát sau khi chạy
};