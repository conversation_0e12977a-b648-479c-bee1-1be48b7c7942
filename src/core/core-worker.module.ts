import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ScheduleModule } from '@nestjs/schedule';
import configuration from './config/configuration';
import workerConfiguration from './config/worker.configuration';
import { DatabaseService } from './database/database.service';
import { CacheService } from './cache/cache.service';
import { LoggerService } from './logger/logger.service';

@Global()
@Module({
    imports: [
        ConfigModule.forRoot({
            load: [configuration, workerConfiguration],
            isGlobal: true,
            envFilePath: ['.env.worker', '.env'],
        }),
        TypeOrmModule.forRootAsync({
            imports: [ConfigModule],
            useClass: DatabaseService,
        }),
        BullModule.forRootAsync({
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) => ({
                redis: {
                    host: configService.get('worker.redis.host'),
                    port: configService.get('worker.redis.port'),
                    password: configService.get('worker.redis.password'),
                },
                defaultJobOptions: configService.get('worker.queue.defaultJobOptions'),
            }),
            inject: [ConfigService],
        }),
        ScheduleModule.forRoot(),
    ],
    providers: [ConfigService, DatabaseService, CacheService, LoggerService],
    exports: [
        ConfigService,
        DatabaseService,
        CacheService,
        LoggerService,
        TypeOrmModule,
        BullModule,
        ScheduleModule,
    ],
})
export class CoreWorkerModule { }
