import { registerAs } from '@nestjs/config';

export default registerAs('api', () => ({
  // API-specific configurations
  port: parseInt(process.env.PORT || '3000', 10),

  // Database configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'sports_game',
    synchronize: process.env.NODE_ENV !== 'production',
    logging: process.env.NODE_ENV === 'development',
  },

  // Cache configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || undefined,
  },

  // External API configuration
  apiFootball: {
    url: process.env.API_FOOTBALL_URL || 'https://v3.football.api-sports.io',
    key: process.env.API_FOOTBALL_KEY,
  },

  // Swagger configuration
  swagger: {
    title: 'Sports Game API',
    description: 'API for Sports Game application',
    version: '1.0',
    enabled: process.env.NODE_ENV !== 'production',
  },
}));
