import { registerAs } from '@nestjs/config';

export default registerAs('worker', () => ({
  // Worker-specific configurations

  // Database configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'sports_game',
    synchronize: process.env.NODE_ENV !== 'production',
    logging: process.env.NODE_ENV === 'development',
  },

  // Cache configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || undefined,
  },

  // Queue configuration
  queue: {
    defaultJobOptions: {
      removeOnComplete: 10,
      removeOnFail: 5,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
  },

  // Sync configuration
  sync: {
    batchSize: parseInt(process.env.SYNC_BATCH_SIZE || '100', 10),
    maxIdsPerRequest: parseInt(process.env.MAX_IDS_PER_REQUEST || '20', 10),
    maxConcurrentRequests: parseInt(process.env.MAX_CONCURRENT_REQUESTS || '1', 10),
    liveFixturesInterval: process.env.LIVE_FIXTURES_INTERVAL || '*/10 * * * * *', // Every 10 seconds
  },

  // External API configuration
  apiFootball: {
    url: process.env.API_FOOTBALL_URL || 'https://v3.football.api-sports.io',
    key: process.env.API_FOOTBALL_KEY,
    rateLimitPerMinute: parseInt(process.env.API_RATE_LIMIT || '100', 10),
  },
}));
