import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import configuration from './config/configuration';
import { DatabaseService } from './database/database.service';
import { CacheService } from './cache/cache.service';
import { LoggerService } from './logger/logger.service';

@Global()
@Module({
    imports: [
        ConfigModule.forRoot({
            load: [configuration],
            isGlobal: true,
        }),
        TypeOrmModule.forRootAsync({
            imports: [ConfigModule],
            useClass: DatabaseService,
        }),
        BullModule.forRoot({
            redis: {
                host: process.env.REDIS_HOST || 'localhost',
                port: parseInt(process.env.REDIS_PORT || '6379', 10),
                password: process.env.REDIS_PASSWORD || undefined,
            },
        }),
    ],
    providers: [ConfigService, DatabaseService, CacheService, LoggerService],
    exports: [
        ConfigService,
        DatabaseService,
        CacheService,
        LoggerService,
        TypeOrmModule,
        BullModule,
    ],
})
export class CoreModule { }