import { Injectable, Inject } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { Redis } from 'ioredis';
import configuration from '../config/configuration';

@Injectable()
export class CacheService {
    private readonly redis: Redis;

    constructor(
        @Inject(configuration.KEY)
        private config: ConfigType<typeof configuration>,
    ) {
        this.redis = new Redis({
            host: this.config.redisHost,
            port: this.config.redisPort,
            password: this.config.redisPassword,
        });
    }

    async setCache(key: string, value: string, ttlSeconds: number): Promise<void> {
        await this.redis.set(key, value, 'EX', ttlSeconds);
    }

    async getCache(key: string): Promise<string | null> {
        return this.redis.get(key);
    }
    /**
   * Delete cache keys matching a pattern
   * @param pattern - Pattern to match (e.g., 'fixtures_list_*')
   */
    async deleteByPattern(pattern: string): Promise<void> {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
            await this.redis.del(...keys);
        }
    }
}