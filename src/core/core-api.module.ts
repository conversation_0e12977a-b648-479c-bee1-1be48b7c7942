import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import configuration from './config/configuration';
import apiConfiguration from './config/api.configuration';
import { DatabaseService } from './database/database.service';
import { CacheService } from './cache/cache.service';
import { LoggerService } from './logger/logger.service';

@Global()
@Module({
    imports: [
        ConfigModule.forRoot({
            load: [configuration, apiConfiguration],
            isGlobal: true,
            envFilePath: ['.env.api', '.env'],
        }),
        TypeOrmModule.forRootAsync({
            imports: [ConfigModule],
            useClass: DatabaseService,
        }),
    ],
    providers: [ConfigService, DatabaseService, CacheService, LoggerService],
    exports: [
        ConfigService,
        DatabaseService,
        CacheService,
        LoggerService,
        TypeOrmModule,
    ],
})
export class CoreApiModule { }
