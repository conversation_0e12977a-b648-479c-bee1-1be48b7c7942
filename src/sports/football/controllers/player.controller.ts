import { Controller, Get, Query, Param, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { PlayerService } from '../services/player.service';
import {
    GetTopScorersDto,
    GetTopAssistsDto,
    GetPlayersDto,
    PaginatedPlayersResponse,
    PlayerResponseDto
} from '../models/player.dto';
import { Public } from '../../../auth/core/decorators/auth.decorators';

@Controller('football/players')
@ApiTags('Football Players')
export class PlayerController {
    constructor(private readonly playerService: PlayerService) { }

    @ApiOperation({
        summary: 'Get Top Scorers (Public)',
        description: `
        Get top goal scorers for a specific league and season.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Real-time top scorer rankings
        - Detailed player statistics
        - Team and league information
        - Cached for performance (1 hour)
        - Pagination support

        **Use Cases:**
        - League top scorer tables
        - Player performance analysis
        - Fantasy football data
        - Sports journalism
        - Mobile app leaderboards
        - Website statistics displays

        **Examples:**
        - /football/players/topscorers?league=39&season=2024 (Premier League 2024)
        - /football/players/topscorers?league=140&season=2024 (La Liga 2024)
        - /football/players/topscorers?league=78&season=2024&page=2&limit=10 (Bundesliga with pagination)
        `
    })
    @ApiResponse({
        status: 200,
        description: 'Top scorers retrieved successfully',
        type: PaginatedPlayersResponse
    })
    @ApiResponse({
        status: 404,
        description: 'No top scorers found',
        example: {
            message: 'No top scorers found for league 39 and season 2024',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters',
        example: {
            message: ['league must be a number', 'season must be a number'],
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @Public()
    @Get('topscorers')
    async getTopScorers(@Query() query: GetTopScorersDto): Promise<PaginatedPlayersResponse> {
        return this.playerService.getTopScorers(query);
    }

    @ApiOperation({
        summary: 'Get Top Assists (Public)',
        description: `
        Get top assist providers for a specific league and season.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Real-time top assists rankings
        - Detailed player statistics
        - Team and league information
        - Cached for performance (1 hour)
        - Pagination support

        **Use Cases:**
        - League top assists tables
        - Player creativity analysis
        - Fantasy football data
        - Sports journalism
        - Mobile app leaderboards
        - Website statistics displays

        **Examples:**
        - /football/players/topassists?league=39&season=2024 (Premier League 2024)
        - /football/players/topassists?league=140&season=2024 (La Liga 2024)
        - /football/players/topassists?league=78&season=2024&page=2&limit=10 (Bundesliga with pagination)
        `
    })
    @ApiResponse({
        status: 200,
        description: 'Top assists retrieved successfully',
        type: PaginatedPlayersResponse
    })
    @ApiResponse({
        status: 404,
        description: 'No top assists found',
        example: {
            message: 'No top assists found for league 39 and season 2024',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters',
        example: {
            message: ['league must be a number', 'season must be a number'],
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @Public()
    @Get('topassists')
    async getTopAssists(@Query() query: GetTopAssistsDto): Promise<PaginatedPlayersResponse> {
        return this.playerService.getTopAssists(query);
    }

    @ApiOperation({
        summary: 'Search Players (Public)',
        description: `
        Search and filter players by various criteria.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Search by player name
        - Filter by team, league, season
        - Detailed player information
        - Cached for performance (30 minutes)
        - Pagination support

        **Query Parameters:**
        - team: Filter by team ID
        - league: Filter by league ID
        - season: Filter by season year
        - search: Search by player name (case-insensitive)

        **Use Cases:**
        - Player search functionality
        - Team roster displays
        - League player listings
        - Player discovery
        - Mobile app search
        - Website player directories

        **Examples:**
        - /football/players?search=haaland (Search for Haaland)
        - /football/players?team=33&season=2024 (Manchester United 2024 players)
        - /football/players?league=39&season=2024 (Premier League 2024 players)
        - /football/players?search=messi&league=140 (Search Messi in La Liga)
        `
    })
    @ApiResponse({
        status: 200,
        description: 'Players retrieved successfully',
        type: PaginatedPlayersResponse
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters',
        example: {
            message: ['page must be a positive number'],
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @Public()
    @Get()
    async searchPlayers(@Query() query: GetPlayersDto): Promise<PaginatedPlayersResponse> {
        return this.playerService.searchPlayers(query);
    }

    @ApiOperation({
        summary: 'Get Player by ID (Public)',
        description: `
        Retrieve detailed information for a specific player by external ID.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Complete player profile
        - Detailed statistics
        - Career information
        - Team and league history
        - Cached for performance (24 hours)

        **Query Parameters:**
        - season: Optional season filter for statistics

        **Use Cases:**
        - Player profile pages
        - Player detail views
        - Career statistics display
        - Mobile app player details
        - Website player information
        - Third-party integrations

        **Examples:**
        - /football/players/276 (Erling Haaland)
        - /football/players/154 (Lionel Messi)
        - /football/players/276?season=2024 (Haaland 2024 season only)
        `
    })
    @ApiParam({
        name: 'externalId',
        description: 'Player external ID',
        example: 276,
        type: Number
    })
    @ApiQuery({
        name: 'season',
        required: false,
        description: 'Season year (optional)',
        example: 2024,
        type: Number
    })
    @ApiResponse({
        status: 200,
        description: 'Player retrieved successfully',
        type: PlayerResponseDto
    })
    @ApiResponse({
        status: 404,
        description: 'Player not found',
        example: {
            message: 'Player with externalId 276 not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @Public()
    @Get(':externalId')
    async getPlayerById(
        @Param('externalId', ParseIntPipe) externalId: number,
        @Query('season') season?: number,
    ): Promise<PlayerResponseDto> {
        const seasonNumber = season ? parseInt(season.toString(), 10) : undefined;
        return this.playerService.getPlayerById(externalId, seasonNumber);
    }
}
