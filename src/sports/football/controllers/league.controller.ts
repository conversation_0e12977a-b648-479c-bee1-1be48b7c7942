import { Controller, Get, Post, Patch, Param, Query, Body, UseGuards } from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiParam,
    ApiQuery,
    ApiBody,
    ApiBearerAuth,
} from '@nestjs/swagger';
import { LeagueService } from '../services/league.service';
import { GetLeaguesDto, CreateLeagueDto, UpdateLeagueDto, LeagueResponseDto, PaginatedLeaguesResponse } from '../models/league.dto';
import { SystemJwtAuthGuard } from '../../../auth/system/guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../../../auth/system/guards/system-roles.guard';
import { TierAccessGuard } from '../../../auth/users/guards/tier-access.guard';
import { Public, EditorPlus } from '../../../auth/core/decorators/auth.decorators';

@ApiTags('Football - Leagues')
@Controller('football/leagues')
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard, TierAccessGuard)
export class LeagueController {
    constructor(private readonly leagueService: LeagueService) { }

    @ApiOperation({
        summary: 'Get All Leagues',
        description: `
        Retrieve all football leagues with filtering and pagination.

        **Features:**
        - Complete league database
        - Country-based filtering
        - Active/inactive status filtering
        - Search by league name or country
        - Pagination support
        - No authentication required

        **Search Examples:**
        - ?search=Premier (Find Premier League)
        - ?search=England (Find English leagues)
        - ?search=Liga (Find La Liga, Liga MX, etc.)
        - ?search=Champions (Find Champions League)

        **Popular Leagues:**
        - Premier League (England) - ID: 39
        - La Liga (Spain) - ID: 140
        - Serie A (Italy) - ID: 135
        - Bundesliga (Germany) - ID: 78
        - Ligue 1 (France) - ID: 61
        `
    })
    @ApiQuery({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number for pagination',
        example: 1
    })
    @ApiQuery({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of leagues per page',
        example: 20
    })
    @ApiQuery({
        name: 'country',
        required: false,
        type: String,
        description: 'Filter by country name',
        example: 'England'
    })
    @ApiQuery({
        name: 'active',
        required: false,
        type: Boolean,
        description: 'Filter by active status',
        example: true
    })
    @ApiQuery({
        name: 'isHot',
        required: false,
        type: Boolean,
        description: 'Filter by hot/popular leagues',
        example: true
    })
    @ApiQuery({
        name: 'search',
        required: false,
        type: String,
        description: 'Search leagues by name or country (case-insensitive)',
        example: 'Premier'
    })
    @ApiResponse({
        status: 200,
        description: 'Leagues retrieved successfully',
        example: {
            data: [
                {
                    id: 1,
                    externalId: 39,
                    name: 'Premier League',
                    country: 'England',
                    logo: 'https://media.api-sports.io/football/leagues/39.png',
                    flag: 'https://media.api-sports.io/flags/gb.svg',
                    season: 2025,
                    active: true,
                    type: 'League',
                    isHot: true
                }
            ],
            meta: {
                totalItems: 150,
                totalPages: 8,
                currentPage: 1,
                limit: 20
            }
        }
    })
    @Public()
    @Get()
    async getLeagues(@Query() query: GetLeaguesDto): Promise<PaginatedLeaguesResponse> {
        return this.leagueService.getLeagues(query);
    }

    @ApiOperation({
        summary: 'Get League by ID',
        description: `
        Retrieve detailed information for a specific league by external ID.

        **Features:**
        - Complete league information
        - Season-specific data
        - Team listings for the league
        - Authentication required for API usage tracking

        **Tier Access:**
        - Free: 100 API calls/month
        - Premium: 10,000 API calls/month
        - Enterprise: Unlimited API calls

        **Use Cases:**
        - League profile pages
        - Season information display
        - Team listings for league
        - League statistics and data
        `
    })
    @ApiParam({
        name: 'externalId',
        type: 'number',
        description: 'League external ID (positive integer)',
        example: 39
    })
    @ApiQuery({
        name: 'season',
        required: false,
        type: Number,
        description: 'Season year (optional)',
        example: 2024
    })
    @ApiResponse({
        status: 200,
        description: 'League retrieved successfully',
        example: {
            id: 1,
            externalId: 39,
            name: 'Premier League',
            country: 'England',
            logo: 'https://media.api-sports.io/football/leagues/39.png',
            flag: 'https://media.api-sports.io/flags/gb.svg',
            season: 2025,
            active: true,
            type: 'League'
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid league ID',
        example: {
            message: 'Invalid externalId: must be a positive integer',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Authentication required',
        example: {
            message: 'System authentication required',
            error: 'Unauthorized',
            statusCode: 401
        }
    })
    @ApiResponse({
        status: 404,
        description: 'League not found',
        example: {
            message: 'League not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @ApiBearerAuth('bearer')
    @Get(':externalId')
    async getLeagueById(
        @Param('externalId') externalId: number,
        @Query('season') season?: number,
    ): Promise<LeagueResponseDto | PaginatedLeaguesResponse> {
        return this.leagueService.getLeagueById(externalId, season);
    }

    @ApiOperation({
        summary: 'Create League (Editor+)',
        description: `
        Create a new league in the system.

        **Access:** Editor+ (Editor and Admin roles)
        **Features:**
        - Add new leagues to database
        - Complete league information
        - Validation and error handling
        - Audit logging

        **Use Cases:**
        - Add new leagues from API Football
        - Manual league creation
        - System administration
        - Data management
        `
    })
    @ApiBody({
        type: CreateLeagueDto,
        description: 'League creation data',
        examples: {
            premierLeague: {
                summary: 'Premier League Example',
                value: {
                    externalId: 39,
                    name: 'Premier League',
                    country: 'England',
                    logo: 'https://media.api-sports.io/football/leagues/39.png',
                    flag: 'https://media.api-sports.io/flags/gb.svg',
                    season: 2025,
                    active: true,
                    type: 'League',
                    isHot: true
                }
            }
        }
    })
    @ApiResponse({
        status: 201,
        description: 'League created successfully',
        type: LeagueResponseDto
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid league data',
        example: {
            message: 'Validation failed',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Authentication required',
        example: {
            message: 'System authentication required',
            error: 'Unauthorized',
            statusCode: 401
        }
    })
    @ApiResponse({
        status: 403,
        description: 'Forbidden - Editor+ access required',
        example: {
            message: 'Forbidden resource',
            error: 'Forbidden',
            statusCode: 403
        }
    })
    @ApiResponse({
        status: 409,
        description: 'League already exists',
        example: {
            message: 'League with external ID 39 already exists',
            error: 'Conflict',
            statusCode: 409
        }
    })
    @ApiBearerAuth('bearer')
    @EditorPlus()
    @Post()
    async createLeague(@Body() createLeagueDto: CreateLeagueDto): Promise<LeagueResponseDto> {
        return this.leagueService.createLeague(createLeagueDto);
    }

    @ApiOperation({
        summary: 'Update League (Editor+)',
        description: `
        Update an existing league in the system.

        **Access:** Editor+ (Editor and Admin roles)
        **Features:**
        - Update league information
        - Partial updates supported
        - Validation and error handling
        - Audit logging

        **Use Cases:**
        - Update league details
        - Activate/deactivate leagues
        - Correct league information
        - System maintenance
        `
    })
    @ApiParam({
        name: 'id',
        type: 'number',
        description: 'League internal ID (positive integer)',
        example: 1
    })
    @ApiBody({
        type: UpdateLeagueDto,
        description: 'League update data (partial)',
        examples: {
            updateActive: {
                summary: 'Update Active Status',
                value: {
                    active: false
                }
            },
            updateSeason: {
                summary: 'Update Season',
                value: {
                    season: 2025,
                    active: true
                }
            },
            updateIsHot: {
                summary: 'Mark as Hot League',
                value: {
                    isHot: true
                }
            }
        }
    })
    @ApiResponse({
        status: 200,
        description: 'League updated successfully',
        type: LeagueResponseDto
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid league data or ID',
        example: {
            message: 'Invalid league ID',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Authentication required',
        example: {
            message: 'System authentication required',
            error: 'Unauthorized',
            statusCode: 401
        }
    })
    @ApiResponse({
        status: 403,
        description: 'Forbidden - Editor+ access required',
        example: {
            message: 'Forbidden resource',
            error: 'Forbidden',
            statusCode: 403
        }
    })
    @ApiResponse({
        status: 404,
        description: 'League not found',
        example: {
            message: 'League not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @ApiBearerAuth('bearer')
    @EditorPlus()
    @Patch(':id')
    async updateLeague(
        @Param('id') id: number,
        @Body() updateLeagueDto: UpdateLeagueDto,
    ): Promise<LeagueResponseDto> {
        return this.leagueService.updateLeague(id, updateLeagueDto);
    }
}