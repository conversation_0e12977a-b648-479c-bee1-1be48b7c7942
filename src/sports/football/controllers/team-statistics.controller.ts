import { Controller, Get, Query } from '@nestjs/common';
import { TeamStatisticsService } from '../services/team-statistics.service';
import { GetTeamStatisticsDto, TeamStatisticsResponseDto } from '../models/team-statistics.dto';

@Controller('football/teams/statistics')
export class TeamStatisticsController {
    constructor(private readonly teamStatisticsService: TeamStatisticsService) { }

    /**
     * Get team statistics by league, season, and team
     * @param query - Query parameters (league, season, team)
     * @returns Team statistics
     */
    @Get()
    async getTeamStatistics(
        @Query() query: GetTeamStatisticsDto,
    ): Promise<{ data: TeamStatisticsResponseDto; status: number }> {
        return this.teamStatisticsService.getTeamStatistics(query);
    }
}