import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SeasonSyncService } from './services/season-sync.service';
import { Fixture } from './models/fixture.entity';
import { League } from './models/league.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([Fixture, League]),
    ],
    providers: [SeasonSyncService],
    exports: [SeasonSyncService],
})
export class SeasonSyncModule { }