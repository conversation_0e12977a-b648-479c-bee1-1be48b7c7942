import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FixtureService } from './services/fixture.service';
import { FixtureStatisticsService } from './services/fixture-statistics.service';
import { LeagueService } from './services/league.service';
import { TeamService } from './services/team.service';
import { TeamStatisticsService } from './services/team-statistics.service';
import { SeasonSyncModule } from './season-sync.module';
import { Fixture } from './models/fixture.entity';
import { League } from './models/league.entity';
import { Team } from './models/team.entity';
import { FixtureStatistics } from './models/fixture-statistics.entity';
import { TeamStatistics } from './models/team-statistics.entity';

// Base Football Module - Contains shared services and entities
@Module({
  imports: [
    TypeOrmModule.forFeature([Fixture, League, Team, FixtureStatistics, TeamStatistics]),
    SeasonSyncModule,
  ],
  providers: [
    FixtureService,
    LeagueService,
    TeamService,
    FixtureStatisticsService,
    TeamStatisticsService,
  ],
  exports: [
    FixtureService,
    LeagueService,
    TeamService,
    FixtureStatisticsService,
    TeamStatisticsService,
    SeasonSyncModule,
    TypeOrmModule,
  ],
})
export class FootballModule { }