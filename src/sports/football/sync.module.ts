import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ScheduleModule } from '@nestjs/schedule';
import { SyncService } from './services/sync.service';
import { SyncProcessor } from './sync.processor';
import { Fixture } from './models/fixture.entity';
import { League } from './models/league.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([Fixture, League]),
        BullModule.registerQueue({ name: 'sync-queue' }),
        ScheduleModule.forRoot(),
    ],
    providers: [SyncService, SyncProcessor],
    exports: [SyncService],
})
export class SyncModule { }