import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { TeamStatistics } from '../models/team-statistics.entity';
import { GetTeamStatisticsDto, TeamStatisticsResponseDto } from '../models/team-statistics.dto';
import { CacheService } from '../../../core/cache/cache.service';

@Injectable()
export class TeamStatisticsService {
    private readonly logger = new Logger(TeamStatisticsService.name);

    constructor(
        @InjectRepository(TeamStatistics)
        private readonly teamStatisticsRepository: Repository<TeamStatistics>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
    ) { }

    /**
     * Get team statistics by league, season, and team
     * @param query - Query parameters (league, season, team)
     * @returns Team statistics
     */
    async getTeamStatistics(query: GetTeamStatisticsDto): Promise<{ data: TeamStatisticsResponseDto; status: number }> {
        const cacheKey = `team_statistics_${query.league}_${query.season}_${query.team}`;
        const cached = await this.cacheService.getCache(cacheKey);
        const newdb = query.newdb !== undefined ? query.newdb : 'all';
        if (cached && newdb !== true) {
            this.logger.debug(`Returning team statistics from cache for key: ${cacheKey}`);
            return { data: JSON.parse(cached), status: 200 };
        }

        let stats = await this.teamStatisticsRepository.findOne({
            where: { teamId: query.team, leagueId: query.league, season: query.season },
        });

        if (!stats) {
            this.logger.debug(`Team statistics not found in DB for query: ${JSON.stringify(query)}, fetching from API`);
            stats = await this.fetchFromApi(query);
            if (!stats) {
                throw new NotFoundException(`Statistics for team ${query.team} in league ${query.league} season ${query.season} not found`);
            }
            try {
                await this.teamStatisticsRepository.save(stats);
                this.logger.debug(`Saved team statistics for team ${query.team} to DB`);
            } catch (error) {
                this.logger.error(`Failed to save team statistics to DB: ${error.message}`);
            }
        }

        const response: TeamStatisticsResponseDto = this.mapToResponseDto(stats);
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), 604800);
        this.logger.debug(`Cached team statistics for key: ${cacheKey}`);
        return { data: response, status: 200 };
    }

    private async fetchFromApi(query: GetTeamStatisticsDto): Promise<TeamStatistics | null> {
        try {
            const apiQuery = {
                league: query.league,
                season: query.season,
                team: query.team,
            };

            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/teams/statistics`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                this.logger.debug(`Calling API: ${apiUrl} with params: ${JSON.stringify(apiQuery)}`);
                return axios.get(apiUrl, {
                    params: apiQuery,
                    headers,
                });
            });

            this.logger.debug(`API response for query ${JSON.stringify(apiQuery)}: ${JSON.stringify(response.data)}`);

            if (!response.data.response) {
                this.logger.warn(`No statistics returned from API for query ${JSON.stringify(apiQuery)}`);
                return null;
            }

            const apiData = response.data.response;
            const stats = this.teamStatisticsRepository.create({
                teamId: query.team,
                leagueId: query.league,
                season: query.season,
                statistics: {
                    played: apiData.fixtures?.played || { home: 0, away: 0, total: 0 },
                    wins: apiData.fixtures?.wins || { home: 0, away: 0, total: 0 },
                    draws: apiData.fixtures?.draws || { home: 0, away: 0, total: 0 },
                    loses: apiData.fixtures?.loses || { home: 0, away: 0, total: 0 },
                    goals: {
                        for: apiData.goals?.for || { home: 0, away: 0, total: 0 },
                        against: apiData.goals?.against || { home: 0, away: 0, total: 0 },
                    },
                    biggest: apiData.biggest || {
                        streak: { wins: 0, draws: 0, loses: 0 },
                        wins: { home: '', away: '' },
                        loses: { home: '', away: '' },
                        goals: {
                            for: { home: 0, away: 0 },
                            against: { home: 0, away: 0 },
                        },
                    },
                    clean_sheet: apiData.clean_sheet || { home: 0, away: 0, total: 0 },
                    failed_to_score: apiData.failed_to_score || { home: 0, away: 0, total: 0 },
                    penalty: apiData.penalty || { scored: 0, missed: 0, total: 0 },
                    cards: {
                        yellow: apiData.cards?.yellow || { minute: {} },
                        red: apiData.cards?.red || { minute: {} },
                    },
                    lineups: apiData.lineups || [],
                },
            });

            return stats;
        } catch (error) {
            this.logger.error(`Failed to fetch from API: ${error.message}`);
            return null;
        }
    }

    private async executeWithRetry<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                if (attempt === retries) {
                    this.logger.error(`Failed after ${retries} attempts: ${error.message}`);
                    throw error;
                }
                this.logger.warn(`Attempt ${attempt} failed: ${error.message}. Retrying after ${delay}ms...`);
                await new Promise((resolve) => setTimeout(resolve, delay));
            }
        }
        throw new Error('Unexpected error in retry logic');
    }

    private mapToResponseDto(stats: TeamStatistics): TeamStatisticsResponseDto {
        return {
            teamId: stats.teamId,
            leagueId: stats.leagueId,
            season: stats.season,
            statistics: stats.statistics,
        };
    }
}