import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { Team } from '../models/team.entity';
import { GetTeamsDto, TeamResponseDto, PaginatedTeamsResponse } from '../models/team.dto';
import { CacheService } from '../../../core/cache/cache.service';
import { ImageService } from '../../../shared/services/image.service';

@Injectable()
export class TeamService {
    private readonly logger = new Logger(TeamService.name);

    constructor(
        @InjectRepository(Team)
        private readonly teamRepository: Repository<Team>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly imageService: ImageService,
    ) { }

    async getTeamById(externalId: number): Promise<TeamResponseDto> {
        const cacheKey = `team_detail_${externalId}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning team from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        let team = await this.teamRepository.findOne({ where: { externalId } });

        if (!team) {
            this.logger.debug(`Team ${externalId} not found in DB, fetching from API`);
            const teams = await this.fetchFromApi({ id: externalId } as any);
            if (teams.length === 0) {
                throw new NotFoundException(`Team with externalId ${externalId} not found`);
            }
            team = teams[0];
            try {
                await this.teamRepository.upsert(team, ['externalId']);
                this.logger.debug(`Upserted team ${externalId} to DB`);
            } catch (error) {
                this.logger.error(`Failed to upsert team to DB: ${error.message}`);
            }
        }

        const response = this.mapToResponseDto([team])[0];
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), 604800);
        this.logger.debug(`Cached team response for key: ${cacheKey}`);
        return response;
    }
    /**
     * Get teams by query parameters
     * @param query - Query parameters (league, season, country, page, limit)
     * @returns Paginated list of teams
     */
    async getTeams(query: GetTeamsDto): Promise<PaginatedTeamsResponse> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const searchKey = query.search ?? '';
        const cacheKey = `teams_list_${query.league ?? ''}_${query.season ?? ''}_${query.country ?? ''}_${searchKey}_${page}_${limit}`;

        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning teams from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        let { teams, totalItems } = await this.fetchFromDb(query);
        if (teams.length === 0) {
            this.logger.debug(`No teams found in DB for query: ${JSON.stringify(query)}, fetching from API`);
            teams = await this.fetchFromApi(query);
            if (teams.length > 0) {
                try {
                    await this.teamRepository.upsert(teams, ['externalId']);
                    this.logger.debug(`Upserted ${teams.length} teams to DB`);
                    const paginatedResult = await this.fetchFromDb(query);
                    teams = paginatedResult.teams;
                    totalItems = paginatedResult.totalItems;
                } catch (error) {
                    this.logger.error(`Failed to upsert teams: ${error.message}`);
                }
            }
        }

        const response: PaginatedTeamsResponse = {
            data: this.mapToResponseDto(teams),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        if (response.data.length > 0) {
            await this.cacheService.setCache(cacheKey, JSON.stringify(response), 604800);
            this.logger.debug(`Cached paginated response for key: ${cacheKey}`);
        }
        return response;
    }

    private async fetchFromDb(query: GetTeamsDto): Promise<{ teams: Team[]; totalItems: number }> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const skip = (page - 1) * limit;

        const qb = this.teamRepository.createQueryBuilder('team');
        if (query.league) {
            qb.andWhere('team.leagueId = :league', { league: query.league });
        }
        if (query.season) {
            qb.andWhere('team.season = :season', { season: query.season });
        }
        if (query.country) {
            qb.andWhere('team.country = :country', { country: query.country.toLowerCase() });
        }
        if (query.search) {
            // Search in team name, country, and code (case-insensitive)
            const searchTerm = `%${query.search.toLowerCase()}%`;
            qb.andWhere(
                '(LOWER(team.name) LIKE :searchTerm OR LOWER(team.country) LIKE :searchTerm OR LOWER(team.code) LIKE :searchTerm)',
                { searchTerm }
            );
        }

        const [teams, totalItems] = await qb
            .skip(skip)
            .take(limit)
            .getManyAndCount();

        this.logger.debug(`Fetched ${teams.length} teams from DB for query: ${JSON.stringify(query)}`);
        return { teams, totalItems };
    }

    private async fetchFromApi(query: GetTeamsDto | { id: number }): Promise<Team[]> {
        try {
            const apiQuery: any = {};
            if ('id' in query) {
                apiQuery.id = query.id;
            } else {
                const { page, limit, ...rest } = query as GetTeamsDto;
                Object.assign(apiQuery, rest);
                if (apiQuery.country) {
                    apiQuery.country = apiQuery.country.charAt(0).toUpperCase() + apiQuery.country.slice(1).toLowerCase();
                }
            }

            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/teams`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                this.logger.debug(`Calling API: ${apiUrl} with params: ${JSON.stringify(apiQuery)}`);
                return axios.get(apiUrl, {
                    params: apiQuery,
                    headers,
                });
            });

            this.logger.debug(`API response for query ${JSON.stringify(apiQuery)}: ${JSON.stringify(response.data)}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No data returned from API for query ${JSON.stringify(apiQuery)}`);
                return [];
            }

            const teams = await Promise.all(
                response.data.response.map(async (apiData: any): Promise<Team | null> => {
                    // Kiểm tra kỹ hơn để tránh lỗi undefined
                    if (!apiData?.team || typeof apiData.team.id !== 'number') {
                        this.logger.warn(`Invalid team data: ${JSON.stringify(apiData)}`);
                        return null;
                    }

                    const logoPath = apiData.team.logo
                        ? await this.imageService.downloadImage(apiData.team.logo, 'teams', `${apiData.team.id}.png`)
                        : '';
                    const imageVenuePath = apiData.venue.image
                        ? await this.imageService.downloadImage(apiData.venue.image, 'venues', `${apiData.team.id}.svg`)
                        : '';
                    const team = this.teamRepository.create({
                        externalId: apiData.team.id,
                        name: apiData.team.name || 'Unknown',
                        code: apiData.team.code || null,
                        country: (apiData.team.country || 'Unknown').toLowerCase(),
                        logo: logoPath,
                        season: ('season' in query ? query.season : 0) || 0,
                        leagueId: ('league' in query ? query.league : null) || null,
                        founded: apiData.team.founded || null,
                        national: apiData.team.national || false,
                        venue: apiData.venue
                            ? {
                                id: apiData.venue.id,
                                name: apiData.venue.name,
                                address: apiData.venue.address,
                                city: apiData.venue.city,
                                capacity: apiData.venue.capacity,
                                surface: apiData.venue.surface,
                                image: imageVenuePath,
                            }
                            : null,
                    } as Team);

                    return team;
                }),
            );

            const validTeams: Team[] = teams.filter((team): team is Team => team !== null);
            this.logger.debug(`Processed ${validTeams.length} valid teams from API`);
            return validTeams;
        } catch (error) {
            this.logger.error(`Failed to fetch from API: ${error.message}`);
            return [];
        }
    }

    private async executeWithRetry<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                if (attempt === retries) {
                    this.logger.error(`Failed after ${retries} attempts: ${error.message}`);
                    throw error;
                }
                this.logger.warn(`Attempt ${attempt} failed: ${error.message}. Retrying after ${delay}ms...`);
                await new Promise((resolve) => setTimeout(resolve, delay));
            }
        }
        throw new Error('Unexpected error in retry logic');
    }

    private mapToResponseDto(teams: Team[]): TeamResponseDto[] {
        return teams.map(team => ({
            id: team.id,
            externalId: team.externalId,
            name: team.name,
            code: team.code,
            country: team.country,
            logo: team.logo,
            season: team.season,
            leagueId: team.leagueId,
            founded: team.founded,
            national: team.national,
            venue: team.venue,
        }));
    }
}