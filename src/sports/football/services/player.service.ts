import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import axios from 'axios';
import { Player } from '../models/player.entity';
import { PlayerStatistics } from '../models/player-statistics.entity';
import {
    GetTopScorersDto,
    GetTopAssistsDto,
    GetPlayersDto,
    PaginatedPlayersResponse,
    PlayerResponseDto
} from '../models/player.dto';
import { CacheService } from '../../../core';

@Injectable()
export class PlayerService {
    private readonly logger = new Logger(PlayerService.name);

    constructor(
        @InjectRepository(Player)
        private readonly playerRepository: Repository<Player>,
        @InjectRepository(PlayerStatistics)
        private readonly playerStatisticsRepository: Repository<PlayerStatistics>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly dataSource: DataSource,
    ) { }

    /**
     * Get top scorers for a league and season
     */
    async getTopScorers(query: GetTopScorersDto): Promise<PaginatedPlayersResponse> {
        const { league, season, page = 1, limit = 20 } = query;
        const cacheKey = `top_scorers_${league}_${season}_${page}_${limit}`;

        // Check cache first
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning top scorers from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        // Try to get from database first
        let players = await this.getTopScorersFromDb(league, season, page, limit);

        // If not found in DB, fetch from API
        if (!players || players.length === 0) {
            this.logger.debug(`Top scorers not found in DB for league ${league} season ${season}, fetching from API`);
            const apiPlayers = await this.fetchTopScorersFromApi(league, season);

            if (apiPlayers.length > 0) {
                await this.savePlayersAndStatistics(apiPlayers);
                players = await this.getTopScorersFromDb(league, season, page, limit);
            }
        }

        if (!players || players.length === 0) {
            throw new NotFoundException(`No top scorers found for league ${league} and season ${season}`);
        }

        // Get total count for pagination
        const totalItems = await this.getTopScorersCount(league, season);
        const totalPages = Math.ceil(totalItems / limit);

        const result: PaginatedPlayersResponse = {
            data: this.mapToResponseDto(players),
            meta: {
                totalItems,
                totalPages,
                currentPage: page,
                limit
            },
            status: 200
        };

        // Cache the result for 1 hour
        await this.cacheService.setCache(cacheKey, JSON.stringify(result), 3600);

        return result;
    }

    /**
     * Get top assists for a league and season
     */
    async getTopAssists(query: GetTopAssistsDto): Promise<PaginatedPlayersResponse> {
        const { league, season, page = 1, limit = 20 } = query;
        const cacheKey = `top_assists_${league}_${season}_${page}_${limit}`;

        // Check cache first
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning top assists from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        // Try to get from database first
        let players = await this.getTopAssistsFromDb(league, season, page, limit);

        // If not found in DB, fetch from API
        if (!players || players.length === 0) {
            this.logger.debug(`Top assists not found in DB for league ${league} season ${season}, fetching from API`);
            const apiPlayers = await this.fetchTopAssistsFromApi(league, season);

            if (apiPlayers.length > 0) {
                await this.savePlayersAndStatistics(apiPlayers);
                players = await this.getTopAssistsFromDb(league, season, page, limit);
            }
        }

        if (!players || players.length === 0) {
            throw new NotFoundException(`No top assists found for league ${league} and season ${season}`);
        }

        // Get total count for pagination
        const totalItems = await this.getTopAssistsCount(league, season);
        const totalPages = Math.ceil(totalItems / limit);

        const result: PaginatedPlayersResponse = {
            data: this.mapToResponseDto(players),
            meta: {
                totalItems,
                totalPages,
                currentPage: page,
                limit
            },
            status: 200
        };

        // Cache the result for 1 hour
        await this.cacheService.setCache(cacheKey, JSON.stringify(result), 3600);

        return result;
    }

    /**
     * Get player by external ID
     */
    async getPlayerById(externalId: number, season?: number): Promise<PlayerResponseDto> {
        const cacheKey = `player_detail_${externalId}${season ? `_${season}` : ''}`;

        // Check cache first
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning player from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        // Try to get from database first
        let player = await this.playerRepository.findOne({
            where: { externalId }
        });

        // If not found in DB, fetch from API
        if (!player) {
            this.logger.debug(`Player ${externalId} not found in DB, fetching from API`);
            const apiPlayers = await this.fetchPlayerFromApi(externalId, season);

            if (apiPlayers.length > 0) {
                await this.savePlayersAndStatistics(apiPlayers);
                player = await this.playerRepository.findOne({
                    where: { externalId }
                });
            }
        }

        if (!player) {
            throw new NotFoundException(`Player with externalId ${externalId} not found`);
        }

        const result = this.mapToResponseDto([player])[0];

        // Cache the result for 24 hours
        await this.cacheService.setCache(cacheKey, JSON.stringify(result), 86400);

        return result;
    }

    /**
     * Search players
     */
    async searchPlayers(query: GetPlayersDto): Promise<PaginatedPlayersResponse> {
        const { team, league, season, search, page = 1, limit = 20 } = query;
        const cacheKey = `players_search_${team || ''}_${league || ''}_${season || ''}_${search || ''}_${page}_${limit}`;

        // Check cache first
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning players search from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        // Build query
        const qb = this.playerRepository.createQueryBuilder('player');

        if (search) {
            qb.andWhere('LOWER(player.name) LIKE LOWER(:search)', { search: `%${search}%` });
        }

        // Note: team, league, season filters temporarily disabled
        // Will be re-enabled when PlayerStatistics relationship is restored

        // Get total count
        const totalItems = await qb.getCount();

        // Apply pagination
        const players = await qb
            .skip((page - 1) * limit)
            .take(limit)
            .orderBy('player.name', 'ASC')
            .getMany();

        const totalPages = Math.ceil(totalItems / limit);

        const result: PaginatedPlayersResponse = {
            data: this.mapToResponseDto(players),
            meta: {
                totalItems,
                totalPages,
                currentPage: page,
                limit
            },
            status: 200
        };

        // Cache the result for 30 minutes
        await this.cacheService.setCache(cacheKey, JSON.stringify(result), 1800);

        return result;
    }

    /**
     * Get top scorers from database - temporarily simplified
     */
    private async getTopScorersFromDb(_league: number, _season: number, _page: number, _limit: number): Promise<Player[]> {
        // Temporarily return empty array - will be implemented when PlayerStatistics is ready
        return [];
    }

    /**
     * Get top assists from database - temporarily simplified
     */
    private async getTopAssistsFromDb(_league: number, _season: number, _page: number, _limit: number): Promise<Player[]> {
        // Temporarily return empty array - will be implemented when PlayerStatistics is ready
        return [];
    }

    /**
     * Get count of top scorers - temporarily simplified
     */
    private async getTopScorersCount(_league: number, _season: number): Promise<number> {
        // Temporarily return 0 - will be implemented when PlayerStatistics is ready
        return 0;
    }

    /**
     * Get count of top assists - temporarily simplified
     */
    private async getTopAssistsCount(_league: number, _season: number): Promise<number> {
        // Temporarily return 0 - will be implemented when PlayerStatistics is ready
        return 0;
    }

    /**
     * Fetch top scorers from API
     */
    private async fetchTopScorersFromApi(league: number, season: number): Promise<any[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topscorers`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                this.logger.debug(`Calling API: ${apiUrl} with params: league=${league}, season=${season}`);
                return axios.get(apiUrl, {
                    params: { league, season },
                    headers,
                });
            });

            this.logger.debug(`API response for top scorers league ${league} season ${season}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No top scorers data returned from API for league ${league} season ${season}`);
                return [];
            }

            return response.data.response;
        } catch (error) {
            this.logger.error(`Failed to fetch top scorers from API: ${error.message}`);
            throw error;
        }
    }

    /**
     * Fetch top assists from API
     */
    private async fetchTopAssistsFromApi(league: number, season: number): Promise<any[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topassists`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                this.logger.debug(`Calling API: ${apiUrl} with params: league=${league}, season=${season}`);
                return axios.get(apiUrl, {
                    params: { league, season },
                    headers,
                });
            });

            this.logger.debug(`API response for top assists league ${league} season ${season}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No top assists data returned from API for league ${league} season ${season}`);
                return [];
            }

            return response.data.response;
        } catch (error) {
            this.logger.error(`Failed to fetch top assists from API: ${error.message}`);
            throw error;
        }
    }

    /**
     * Fetch player from API
     */
    private async fetchPlayerFromApi(externalId: number, season?: number): Promise<any[]> {
        try {
            const params: any = { id: externalId };
            if (season) {
                params.season = season;
            }

            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                this.logger.debug(`Calling API: ${apiUrl} with params: ${JSON.stringify(params)}`);
                return axios.get(apiUrl, {
                    params,
                    headers,
                });
            });

            this.logger.debug(`API response for player ${externalId}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No player data returned from API for player ${externalId}`);
                return [];
            }

            return response.data.response;
        } catch (error) {
            this.logger.error(`Failed to fetch player from API: ${error.message}`);
            throw error;
        }
    }

    /**
     * Save players and statistics to database
     */
    private async savePlayersAndStatistics(apiData: any[]): Promise<void> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            for (const item of apiData) {
                const playerData = item.player;
                const statisticsData = item.statistics;

                // Upsert player
                await queryRunner.manager.upsert(
                    Player,
                    {
                        externalId: playerData.id,
                        name: playerData.name,
                        firstName: playerData.firstname,
                        lastName: playerData.lastname,
                        age: playerData.age,
                        birthDate: playerData.birth?.date ? new Date(playerData.birth.date) : undefined,
                        birthPlace: playerData.birth?.place,
                        birthCountry: playerData.birth?.country,
                        nationality: playerData.nationality,
                        height: playerData.height,
                        weight: playerData.weight,
                        injured: playerData.injured || false,
                        photo: playerData.photo,
                        position: statisticsData?.[0]?.games?.position,
                    },
                    ['externalId']
                );

                // Upsert statistics for each team/league/season
                if (statisticsData && Array.isArray(statisticsData)) {
                    for (const stat of statisticsData) {
                        await queryRunner.manager.upsert(
                            PlayerStatistics,
                            {
                                playerId: playerData.id,
                                teamId: stat.team?.id,
                                teamName: stat.team?.name,
                                teamLogo: stat.team?.logo,
                                leagueId: stat.league?.id,
                                leagueName: stat.league?.name,
                                leagueCountry: stat.league?.country,
                                leagueLogo: stat.league?.logo,
                                season: stat.league?.season,
                                games: stat.games,
                                substitutes: stat.substitutes,
                                shots: stat.shots,
                                goals: stat.goals,
                                passes: stat.passes,
                                tackles: stat.tackles,
                                duels: stat.duels,
                                dribbles: stat.dribbles,
                                fouls: stat.fouls,
                                cards: stat.cards,
                                penalty: stat.penalty,
                            },
                            ['playerId', 'teamId', 'leagueId', 'season']
                        );
                    }
                }
            }

            await queryRunner.commitTransaction();
            this.logger.debug(`Successfully saved ${apiData.length} players and their statistics`);
        } catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Failed to save players and statistics: ${error.message}`);
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    /**
     * Map players to response DTOs
     */
    private mapToResponseDto(players: Player[]): any[] {
        return players.map(player => ({
            player: {
                id: player.externalId,
                name: player.name,
                firstname: player.firstName,
                lastname: player.lastName,
                age: player.age,
                birth: {
                    date: player.birthDate?.toISOString().split('T')[0],
                    place: player.birthPlace,
                    country: player.birthCountry
                },
                nationality: player.nationality,
                height: player.height,
                weight: player.weight,
                injured: player.injured,
                photo: player.photo
            },
            statistics: [] // Temporarily disabled - will be implemented later
        }));
    }

    /**
     * Execute API call with retry logic
     */
    private async executeWithRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
        let lastError: Error = new Error('Unknown error');

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error as Error;
                this.logger.warn(`API call attempt ${attempt} failed: ${error.message}`);

                if (attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
                    this.logger.debug(`Retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError;
    }
}
