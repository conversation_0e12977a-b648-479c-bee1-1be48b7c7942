import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository, DataSource } from 'typeorm';
import axios from 'axios';

import { Player } from '../models/player.entity';
import { PlayerStatistics } from '../models/player-statistics.entity';
import {
    GetTopScorersDto,
    GetTopAssistsDto,
    GetPlayersDto,
    PaginatedPlayersResponse,
    PlayerResponseDto
} from '../models/player.dto';
import { CacheService } from '../../../core';

@Injectable()
export class PlayerService {
    private readonly logger = new Logger(PlayerService.name);

    constructor(
        @InjectRepository(Player)
        private readonly playerRepository: Repository<Player>,
        @InjectRepository(PlayerStatistics)
        private readonly playerStatisticsRepository: Repository<PlayerStatistics>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly dataSource: DataSource,
    ) { }

    /**
     * Get top scorers for a league and season
     */
    async getTopScorers(query: GetTopScorersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { league, season, page = 1, limit = 20 } = query;
            this.logger.debug(`Getting top scorers for league ${league}, season ${season}`);

            // For now, return mock data to test endpoint
            const mockPlayers = this.getMockPlayers(league, season);

            const result: PaginatedPlayersResponse = {
                data: mockPlayers.slice(0, limit),
                meta: {
                    totalItems: mockPlayers.length,
                    totalPages: Math.ceil(mockPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopScorers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get top assists for a league and season
     */
    async getTopAssists(query: GetTopAssistsDto): Promise<PaginatedPlayersResponse> {
        try {
            const { league, season, page = 1, limit = 20 } = query;
            this.logger.debug(`Getting top assists for league ${league}, season ${season}`);

            // For now, return mock data
            const mockPlayers = this.getMockPlayers(league, season);

            const result: PaginatedPlayersResponse = {
                data: mockPlayers.slice(0, limit),
                meta: {
                    totalItems: mockPlayers.length,
                    totalPages: Math.ceil(mockPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopAssists: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get player by external ID
     */
    async getPlayerById(externalId: number, season?: number): Promise<PlayerResponseDto> {
        try {
            this.logger.debug(`Getting player by externalId: ${externalId}, season: ${season}`);

            // For now, return mock data
            const mockPlayer = {
                player: {
                    id: externalId,
                    name: "Test Player",
                    firstname: "Test",
                    lastname: "Player",
                    age: 25,
                    birth: {
                        date: "1999-01-01",
                        place: "Test City",
                        country: "Test Country"
                    },
                    nationality: "Test",
                    height: "180 cm",
                    weight: "75 kg",
                    injured: false,
                    photo: "https://example.com/photo.jpg"
                },
                statistics: []
            };

            return mockPlayer;
        } catch (error) {
            this.logger.error(`Error in getPlayerById: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Search players
     */
    async searchPlayers(query: GetPlayersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { team, league, season, search, page = 1, limit = 20 } = query;
            this.logger.debug(`Searching players with filters: team=${team}, league=${league}, season=${season}, search=${search}`);

            // For now, return mock data (skip database query)
            const mockPlayers = this.getMockPlayers(league || 39, season || 2024);

            // Filter mock data based on search if provided
            let filteredPlayers = mockPlayers;
            if (search) {
                filteredPlayers = mockPlayers.filter(item =>
                    item.player.name.toLowerCase().includes(search.toLowerCase())
                );
            }

            const result: PaginatedPlayersResponse = {
                data: filteredPlayers.slice(0, limit),
                meta: {
                    totalItems: filteredPlayers.length,
                    totalPages: Math.ceil(filteredPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in searchPlayers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get mock players data for testing
     */
    private getMockPlayers(league: number, season: number): any[] {
        return [
            {
                player: {
                    id: 276,
                    name: "Erling Haaland",
                    firstname: "Erling",
                    lastname: "Haaland",
                    age: 23,
                    birth: {
                        date: "2000-07-21",
                        place: "Leeds",
                        country: "England"
                    },
                    nationality: "Norway",
                    height: "194 cm",
                    weight: "88 kg",
                    injured: false,
                    photo: "https://media.api-sports.io/football/players/276.png"
                },
                statistics: [
                    {
                        team: {
                            id: 50,
                            name: "Manchester City",
                            logo: "https://media.api-sports.io/football/teams/50.png"
                        },
                        league: {
                            id: league,
                            name: "Premier League",
                            country: "England",
                            logo: "https://media.api-sports.io/football/leagues/39.png",
                            season: season
                        },
                        games: {
                            appearences: 12,
                            lineups: 11,
                            minutes: 1020,
                            position: "Attacker"
                        },
                        goals: {
                            total: 15,
                            assists: 2
                        }
                    }
                ]
            },
            {
                player: {
                    id: 154,
                    name: "Lionel Messi",
                    firstname: "Lionel",
                    lastname: "Messi",
                    age: 36,
                    birth: {
                        date: "1987-06-24",
                        place: "Rosario",
                        country: "Argentina"
                    },
                    nationality: "Argentina",
                    height: "170 cm",
                    weight: "72 kg",
                    injured: false,
                    photo: "https://media.api-sports.io/football/players/154.png"
                },
                statistics: [
                    {
                        team: {
                            id: 1395,
                            name: "Inter Miami",
                            logo: "https://media.api-sports.io/football/teams/1395.png"
                        },
                        league: {
                            id: league,
                            name: "MLS",
                            country: "USA",
                            logo: "https://media.api-sports.io/football/leagues/253.png",
                            season: season
                        },
                        games: {
                            appearences: 10,
                            lineups: 9,
                            minutes: 850,
                            position: "Attacker"
                        },
                        goals: {
                            total: 12,
                            assists: 8
                        }
                    }
                ]
            }
        ];
    }
}
