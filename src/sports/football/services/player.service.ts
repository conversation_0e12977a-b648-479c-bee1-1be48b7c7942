import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository, In } from 'typeorm';
import axios from 'axios';

import { Player } from '../models/player.entity';
import { PlayerStatistics } from '../models/player-statistics.entity';
import {
    GetTopScorersDto,
    GetTopAssistsDto,
    GetPlayersDto,
    PaginatedPlayersResponse,
    PlayerResponseDto
} from '../models/player.dto';
import { CacheService } from '../../../core';
import { ImageService } from '../../../shared/services/image.service';

@Injectable()
export class PlayerService {
    private readonly logger = new Logger(PlayerService.name);
    private readonly BATCH_SIZE = 10; // Process players in batches

    constructor(
        @InjectRepository(Player)
        private readonly playerRepository: Repository<Player>,
        @InjectRepository(PlayerStatistics)
        private readonly playerStatisticsRepository: Repository<PlayerStatistics>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly imageService: ImageService,
    ) { }

    /**
     * Get top scorers for a league and season
     */
    async getTopScorers(query: GetTopScorersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { league, season, page = 1, limit = 20 } = query;
            this.logger.debug(`Getting top scorers for league ${league}, season ${season}`);

            // Fetch from API
            const apiPlayers = await this.fetchTopScorersFromApi(league, season);

            // Save all players to database in parallel batches
            if (apiPlayers.length > 0) {
                await this.savePlayersFromApiBatch(apiPlayers);
                this.logger.debug(`Saved ${apiPlayers.length} top scorers to database`);
            }

            // Apply pagination to API data (since it's already ordered correctly)
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedPlayers = apiPlayers.slice(startIndex, endIndex);

            const result: PaginatedPlayersResponse = {
                data: paginatedPlayers,
                meta: {
                    totalItems: apiPlayers.length,
                    totalPages: Math.ceil(apiPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopScorers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get top assists for a league and season
     */
    async getTopAssists(query: GetTopAssistsDto): Promise<PaginatedPlayersResponse> {
        try {
            const { league, season, page = 1, limit = 20 } = query;
            this.logger.debug(`Getting top assists for league ${league}, season ${season}`);

            // Fetch from API
            const apiPlayers = await this.fetchTopAssistsFromApi(league, season);

            // Save all players to database in parallel batches
            if (apiPlayers.length > 0) {
                await this.savePlayersFromApiBatch(apiPlayers);
                this.logger.debug(`Saved ${apiPlayers.length} top assists to database`);
            }

            // Apply pagination to API data (since it's already ordered correctly)
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedPlayers = apiPlayers.slice(startIndex, endIndex);

            const result: PaginatedPlayersResponse = {
                data: paginatedPlayers,
                meta: {
                    totalItems: apiPlayers.length,
                    totalPages: Math.ceil(apiPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopAssists: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Search players
     */
    async searchPlayers(query: GetPlayersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { search, page = 1, limit = 20 } = query;
            this.logger.debug(`Searching players with query: ${search}`);

            // Query database
            const qb = this.playerRepository.createQueryBuilder('player');

            if (search) {
                qb.andWhere('LOWER(player.name) LIKE LOWER(:search)', { search: `%${search}%` });
            }

            const totalItems = await qb.getCount();
            const players = await qb
                .skip((page - 1) * limit)
                .take(limit)
                .orderBy('player.name', 'ASC')
                .getMany();

            const result: PaginatedPlayersResponse = {
                data: players.map(player => this.mapPlayerToResponse(player)),
                meta: {
                    totalItems,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in searchPlayers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get player by external ID
     */
    async getPlayerById(externalId: number): Promise<PlayerResponseDto> {
        try {
            this.logger.debug(`Getting player by external ID: ${externalId}`);

            // Try database first
            const player = await this.playerRepository.findOne({
                where: { externalId },
                relations: ['statistics']
            });

            if (player) {
                return this.mapPlayerToDetailedResponse(player);
            }

            // If not found, fetch from API
            const apiPlayer = await this.fetchPlayerFromApi(externalId);
            if (apiPlayer) {
                // Save to database
                const savedPlayer = await this.savePlayerFromApi(apiPlayer);
                return this.mapPlayerToDetailedResponse(savedPlayer);
            }

            throw new NotFoundException(`Player with external ID ${externalId} not found`);
        } catch (error) {
            this.logger.error(`Error in getPlayerById: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Fetch top scorers from API
     */
    private async fetchTopScorersFromApi(league: number, season: number): Promise<any[]> {
        try {
            const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topscorers`;
            const apiKey = this.configService.get('app.apiFootballKey');

            this.logger.debug(`Fetching top scorers from API: ${apiUrl}?league=${league}&season=${season}`);

            const response = await axios.get(apiUrl, {
                headers: {
                    'x-apisports-key': apiKey,
                },
                params: {
                    league,
                    season,
                },
                timeout: 15000,
            });

            if (response.data && response.data.response) {
                this.logger.debug(`API returned ${response.data.response.length} top scorers`);
                return response.data.response;
            }

            this.logger.warn(`API returned no data for league ${league} season ${season}`);
            return [];
        } catch (error) {
            this.logger.error(`Error fetching top scorers from API: ${error.message}`);
            return [];
        }
    }

    /**
     * Fetch top assists from API
     */
    private async fetchTopAssistsFromApi(league: number, season: number): Promise<any[]> {
        try {
            const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topassists`;
            const apiKey = this.configService.get('app.apiFootballKey');

            this.logger.debug(`Fetching top assists from API: ${apiUrl}?league=${league}&season=${season}`);

            const response = await axios.get(apiUrl, {
                headers: {
                    'x-apisports-key': apiKey,
                },
                params: {
                    league,
                    season,
                },
                timeout: 15000,
            });

            if (response.data && response.data.response) {
                this.logger.debug(`API returned ${response.data.response.length} top assists`);
                return response.data.response;
            }

            this.logger.warn(`API returned no data for league ${league} season ${season}`);
            return [];
        } catch (error) {
            this.logger.error(`Error fetching top assists from API: ${error.message}`);
            return [];
        }
    }

    /**
     * Fetch single player from API
     */
    private async fetchPlayerFromApi(externalId: number): Promise<any> {
        try {
            const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players`;
            const apiKey = this.configService.get('app.apiFootballKey');

            this.logger.debug(`Fetching player from API: ${apiUrl}?id=${externalId}`);

            const response = await axios.get(apiUrl, {
                headers: {
                    'x-apisports-key': apiKey,
                },
                params: {
                    id: externalId,
                    season: 2024,
                },
                timeout: 15000,
            });

            if (response.data && response.data.response && response.data.response.length > 0) {
                this.logger.debug(`API returned player data for ID ${externalId}`);
                return response.data.response[0];
            }

            this.logger.warn(`API returned no data for player ID ${externalId}`);
            return null;
        } catch (error) {
            this.logger.error(`Error fetching player from API: ${error.message}`);
            return null;
        }
    }

    /**
     * Save multiple players from API data in parallel batches
     */
    private async savePlayersFromApiBatch(apiPlayersData: any[]): Promise<void> {
        try {
            if (apiPlayersData.length === 0) return;

            this.logger.debug(`Starting batch save for ${apiPlayersData.length} players`);

            // Chia players thành batches nhỏ hơn để tránh timeout
            const batches = [];
            for (let i = 0; i < apiPlayersData.length; i += this.BATCH_SIZE) {
                batches.push(apiPlayersData.slice(i, i + this.BATCH_SIZE));
            }

            this.logger.debug(`Processing ${batches.length} batches of players`);

            // Process từng batch song song với Promise.all
            const saveResults = await Promise.all(
                batches.map(async (batch, index) => {
                    try {
                        const savedCount = await this.processBatchPlayers(batch);
                        this.logger.debug(`Batch ${index + 1}/${batches.length}: Saved ${savedCount}/${batch.length} players`);
                        return savedCount;
                    } catch (error) {
                        this.logger.error(`Failed to save batch ${index + 1}: ${error.message}`);
                        return 0;
                    }
                })
            );

            const totalSaved = saveResults.reduce((sum: number, count: number) => sum + count, 0);
            this.logger.debug(`Successfully saved ${totalSaved}/${apiPlayersData.length} players in ${batches.length} batches`);

        } catch (error) {
            this.logger.error(`Failed to save players batch: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Process a batch of players with upsert logic
     */
    private async processBatchPlayers(batchData: any[]): Promise<number> {
        try {
            const playersToSave: Player[] = [];
            const statisticsMap = new Map<number, any[]>(); // Map externalId to statistics

            // Prepare all players and map statistics
            for (const apiData of batchData) {
                const playerData = apiData.player;
                const statisticsData = apiData.statistics || [];

                // Download player photo if available
                const playerPhotoPath = playerData.photo
                    ? await this.imageService.downloadImageBlocking(playerData.photo, 'players', `${playerData.id}.png`)
                    : '';

                // Create player entity
                const player = this.playerRepository.create({
                    externalId: playerData.id,
                    name: playerData.name,
                    firstName: playerData.firstname,
                    lastName: playerData.lastname,
                    age: playerData.age,
                    birthDate: playerData.birth?.date ? new Date(playerData.birth.date) : undefined,
                    birthPlace: playerData.birth?.place,
                    birthCountry: playerData.birth?.country,
                    nationality: playerData.nationality,
                    height: playerData.height,
                    weight: playerData.weight,
                    injured: playerData.injured || false,
                    photo: playerPhotoPath,
                    timestamp: Date.now(),
                });

                playersToSave.push(player);

                // Store statistics for later processing
                if (statisticsData.length > 0) {
                    statisticsMap.set(playerData.id, statisticsData);
                }
            }

            // Upsert players first
            if (playersToSave.length > 0) {
                await this.playerRepository.upsert(playersToSave, ['externalId']);
            }

            // Process statistics if any
            if (statisticsMap.size > 0) {
                const externalIds = Array.from(statisticsMap.keys());
                const savedPlayers = await this.playerRepository.find({
                    where: { externalId: In(externalIds) }
                });

                // Create statistics with correct playerId
                const statisticsToSave: PlayerStatistics[] = [];

                for (const player of savedPlayers) {
                    const playerStats = statisticsMap.get(player.externalId);
                    if (playerStats) {
                        for (const stat of playerStats) {
                            // Download team logo if available
                            const teamLogoPath = stat.team?.logo
                                ? await this.imageService.downloadImageBlocking(stat.team.logo, 'teams', `${stat.team.id}.png`)
                                : '';

                            // Download league logo if available
                            const leagueLogoPath = stat.league?.logo
                                ? await this.imageService.downloadImageBlocking(stat.league.logo, 'leagues', `${stat.league.id}.png`)
                                : '';

                            const playerStat = this.playerStatisticsRepository.create({
                                playerId: player.id,
                                teamId: stat.team?.id,
                                teamName: stat.team?.name,
                                teamLogo: teamLogoPath,
                                leagueId: stat.league?.id,
                                leagueName: stat.league?.name,
                                leagueCountry: stat.league?.country,
                                leagueLogo: leagueLogoPath,
                                season: stat.league?.season,
                                games: stat.games,
                                substitutes: stat.substitutes,
                                shots: stat.shots,
                                goals: stat.goals,
                                passes: stat.passes,
                                tackles: stat.tackles,
                                duels: stat.duels,
                                dribbles: stat.dribbles,
                                fouls: stat.fouls,
                                cards: stat.cards,
                                penalty: stat.penalty,
                            });

                            statisticsToSave.push(playerStat);
                        }
                    }
                }

                // Upsert statistics
                if (statisticsToSave.length > 0) {
                    await this.playerStatisticsRepository.upsert(statisticsToSave, ['playerId', 'teamId', 'leagueId', 'season']);
                }
            }

            return playersToSave.length;

        } catch (error) {
            this.logger.error(`Error processing batch players: ${error.message}`, error.stack);
            throw error;
        }
    }



    /**
     * Save player from API data
     */
    private async savePlayerFromApi(apiData: any): Promise<Player> {
        try {
            const playerData = apiData.player;
            const statisticsData = apiData.statistics || [];

            // Create player entity
            const player = this.playerRepository.create({
                externalId: playerData.id,
                name: playerData.name,
                firstName: playerData.firstname,
                lastName: playerData.lastname,
                age: playerData.age,
                birthDate: playerData.birth?.date ? new Date(playerData.birth.date) : undefined,
                birthPlace: playerData.birth?.place,
                birthCountry: playerData.birth?.country,
                nationality: playerData.nationality,
                height: playerData.height,
                weight: playerData.weight,
                injured: playerData.injured || false,
                photo: playerData.photo,
                timestamp: Date.now(),
            });

            // Save player
            const savedPlayer = await this.playerRepository.save(player);

            // Save statistics if available
            if (statisticsData.length > 0) {
                for (const stat of statisticsData) {
                    const playerStat = this.playerStatisticsRepository.create({
                        playerId: savedPlayer.id,
                        teamId: stat.team?.id,
                        teamName: stat.team?.name,
                        teamLogo: stat.team?.logo,
                        leagueId: stat.league?.id,
                        leagueName: stat.league?.name,
                        leagueCountry: stat.league?.country,
                        leagueLogo: stat.league?.logo,
                        season: stat.league?.season,
                        games: stat.games,
                        substitutes: stat.substitutes,
                        shots: stat.shots,
                        goals: stat.goals,
                        passes: stat.passes,
                        tackles: stat.tackles,
                        duels: stat.duels,
                        dribbles: stat.dribbles,
                        fouls: stat.fouls,
                        cards: stat.cards,
                        penalty: stat.penalty,
                    });

                    await this.playerStatisticsRepository.save(playerStat);
                }
            }

            return savedPlayer;
        } catch (error) {
            this.logger.error(`Error saving player from API: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Map player to simple response
     */
    private mapPlayerToResponse(player: Player): any {
        return {
            player: {
                id: player.externalId,
                name: player.name,
                firstname: player.firstName,
                lastname: player.lastName,
                age: player.age,
                birth: {
                    date: player.birthDate ? (player.birthDate instanceof Date ? player.birthDate.toISOString().split('T')[0] : String(player.birthDate)) : null,
                    place: player.birthPlace,
                    country: player.birthCountry
                },
                nationality: player.nationality,
                height: player.height,
                weight: player.weight,
                injured: player.injured,
                photo: this.convertToPublicUrl(player.photo) || ''
            },
            statistics: []
        };
    }

    /**
     * Map player to detailed response with statistics
     */
    private mapPlayerToDetailedResponse(player: Player): PlayerResponseDto {
        return {
            player: {
                id: player.externalId,
                name: player.name,
                firstname: player.firstName,
                lastname: player.lastName,
                age: player.age,
                birth: {
                    date: player.birthDate ? (player.birthDate instanceof Date ? player.birthDate.toISOString().split('T')[0] : String(player.birthDate)) : null,
                    place: player.birthPlace,
                    country: player.birthCountry
                },
                nationality: player.nationality,
                height: player.height,
                weight: player.weight,
                injured: player.injured,
                photo: this.convertToPublicUrl(player.photo) || '',
                statistics: []
            },
            statistics: (player.statistics || []).map(stat => ({
                team: {
                    id: stat.teamId,
                    name: stat.teamName,
                    logo: this.convertToPublicUrl(stat.teamLogo) || ''
                },
                league: {
                    id: stat.leagueId,
                    name: stat.leagueName,
                    country: stat.leagueCountry,
                    logo: this.convertToPublicUrl(stat.leagueLogo) || '',
                    flag: stat.leagueCountry || '',
                    season: stat.season
                },
                games: stat.games,
                substitutes: stat.substitutes,
                shots: stat.shots,
                goals: stat.goals,
                passes: stat.passes,
                tackles: stat.tackles,
                duels: stat.duels,
                dribbles: stat.dribbles,
                fouls: stat.fouls,
                cards: stat.cards,
                penalty: stat.penalty
            }))
        };
    }

    /**
     * Convert database file path to public URL
     */
    private convertToPublicUrl(filePath: string | null | undefined): string | null {
        if (!filePath) return null;

        // Convert "public/images/players/184.png" to "uploads/players/184.png"
        if (filePath.startsWith('public/images/')) {
            return filePath.replace('public/images/', 'uploads/');
        }

        // If already in uploads format, return as is
        if (filePath.startsWith('uploads/')) {
            return filePath;
        }

        // If it's a full URL, return as is
        if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
            return filePath;
        }

        // Default: assume it's a relative path under public/images
        return `uploads/${filePath}`;
    }
}
