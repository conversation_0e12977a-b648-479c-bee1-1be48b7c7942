import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository, DataSource } from 'typeorm';
import axios from 'axios';

import { Player } from '../models/player.entity';
import { PlayerStatistics } from '../models/player-statistics.entity';
import { Team } from '../models/team.entity';
import { League } from '../models/league.entity';
import {
    GetTopScorersDto,
    GetTopAssistsDto,
    GetPlayersDto,
    PaginatedPlayersResponse,
    PlayerResponseDto
} from '../models/player.dto';
import { CacheService } from '../../../core';

@Injectable()
export class PlayerService {
    private readonly logger = new Logger(PlayerService.name);

    constructor(
        @InjectRepository(Player)
        private readonly playerRepository: Repository<Player>,
        @InjectRepository(PlayerStatistics)
        private readonly playerStatisticsRepository: Repository<PlayerStatistics>,
        @InjectRepository(Team)
        private readonly teamRepository: Repository<Team>,
        @InjectRepository(League)
        private readonly leagueRepository: Repository<League>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly dataSource: DataSource,
    ) { }

    /**
     * Get top scorers for a league and season
     */
    async getTopScorers(query: GetTopScorersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { league, season, page = 1, limit = 20 } = query;
            const cacheKey = `top_scorers_${league}_${season}_${page}_${limit}`;

            this.logger.debug(`Getting top scorers for league ${league}, season ${season}, page ${page}, limit ${limit}`);

            // Check cache first
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning top scorers from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }

            // Try to get from database first
            let { players, totalItems } = await this.getTopScorersFromDb(league, season, page, limit);

            // If not found in DB, fetch from API
            if (players.length === 0) {
                this.logger.debug(`Top scorers not found in DB for league ${league} season ${season}, fetching from API`);
                const apiPlayers = await this.fetchTopScorersFromApi(league, season);

                if (apiPlayers.length > 0) {
                    // Save to database with transaction
                    await this.savePlayersAndStatistics(apiPlayers);

                    // Get from database again for proper pagination
                    const dbResult = await this.getTopScorersFromDb(league, season, page, limit);
                    players = dbResult.players;
                    totalItems = dbResult.totalItems;
                }
            }

            const result: PaginatedPlayersResponse = {
                data: this.mapToResponseDto(players),
                meta: {
                    totalItems,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            // Cache the result for 1 hour
            await this.cacheService.setCache(cacheKey, JSON.stringify(result), 3600);
            this.logger.debug(`Cached top scorers for key: ${cacheKey}`);

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopScorers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get top assists for a league and season
     */
    async getTopAssists(query: GetTopAssistsDto): Promise<PaginatedPlayersResponse> {
        try {
            const { league, season, page = 1, limit = 20 } = query;
            const cacheKey = `top_assists_${league}_${season}_${page}_${limit}`;

            this.logger.debug(`Getting top assists for league ${league}, season ${season}, page ${page}, limit ${limit}`);

            // Check cache first
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning top assists from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }

            // Try to get from database first
            let { players, totalItems } = await this.getTopAssistsFromDb(league, season, page, limit);

            // If not found in DB, fetch from API
            if (players.length === 0) {
                this.logger.debug(`Top assists not found in DB for league ${league} season ${season}, fetching from API`);
                const apiPlayers = await this.fetchTopAssistsFromApi(league, season);

                if (apiPlayers.length > 0) {
                    // Save to database with transaction
                    await this.savePlayersAndStatistics(apiPlayers);

                    // Get from database again for proper pagination
                    const dbResult = await this.getTopAssistsFromDb(league, season, page, limit);
                    players = dbResult.players;
                    totalItems = dbResult.totalItems;
                }
            }

            const result: PaginatedPlayersResponse = {
                data: this.mapToResponseDto(players),
                meta: {
                    totalItems,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            // Cache the result for 1 hour
            await this.cacheService.setCache(cacheKey, JSON.stringify(result), 3600);
            this.logger.debug(`Cached top assists for key: ${cacheKey}`);

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopAssists: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get player by external ID
     */
    async getPlayerById(externalId: number, season?: number): Promise<PlayerResponseDto> {
        try {
            this.logger.debug(`Getting player by externalId: ${externalId}, season: ${season}`);

            // For now, return mock data
            const mockPlayer = {
                player: {
                    id: externalId,
                    name: "Test Player",
                    firstname: "Test",
                    lastname: "Player",
                    age: 25,
                    birth: {
                        date: "1999-01-01",
                        place: "Test City",
                        country: "Test Country"
                    },
                    nationality: "Test",
                    height: "180 cm",
                    weight: "75 kg",
                    injured: false,
                    photo: "https://example.com/photo.jpg"
                },
                statistics: []
            };

            return mockPlayer;
        } catch (error) {
            this.logger.error(`Error in getPlayerById: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Search players
     */
    async searchPlayers(query: GetPlayersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { team, league, season, search, page = 1, limit = 20 } = query;
            this.logger.debug(`Searching players with filters: team=${team}, league=${league}, season=${season}, search=${search}`);

            // For now, return mock data (skip database query)
            const mockPlayers = this.getMockPlayers(league || 39, season || 2024);

            // Filter mock data based on search if provided
            let filteredPlayers = mockPlayers;
            if (search) {
                filteredPlayers = mockPlayers.filter(item =>
                    item.player.name.toLowerCase().includes(search.toLowerCase())
                );
            }

            const result: PaginatedPlayersResponse = {
                data: filteredPlayers.slice(0, limit),
                meta: {
                    totalItems: filteredPlayers.length,
                    totalPages: Math.ceil(filteredPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in searchPlayers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get top scorers from database
     */
    private async getTopScorersFromDb(league: number, season: number, page: number, limit: number): Promise<{ players: Player[], totalItems: number }> {
        try {
            // Query PlayerStatistics to get top scorers, then join with Player
            const statsQb = this.playerStatisticsRepository
                .createQueryBuilder('stats')
                .leftJoinAndSelect('stats.player', 'player')
                .where('stats.leagueId = :league', { league })
                .andWhere('stats.season = :season', { season })
                .andWhere('stats.goals->\'total\' IS NOT NULL')
                .andWhere('CAST(stats.goals->>\'total\' AS INTEGER) > 0')
                .orderBy('CAST(stats.goals->>\'total\' AS INTEGER)', 'DESC')
                .addOrderBy('player.name', 'ASC');

            const totalItems = await statsQb.getCount();
            const statsWithPlayers = await statsQb
                .skip((page - 1) * limit)
                .take(limit)
                .getMany();

            // Extract players from statistics
            const players = statsWithPlayers.map(stat => stat.player).filter(player => player);

            this.logger.debug(`Found ${players.length} top scorers in DB for league ${league} season ${season}`);
            return { players, totalItems };
        } catch (error) {
            this.logger.error(`Error getting top scorers from DB: ${error.message}`);
            return { players: [], totalItems: 0 };
        }
    }

    /**
     * Get top assists from database
     */
    private async getTopAssistsFromDb(league: number, season: number, page: number, limit: number): Promise<{ players: Player[], totalItems: number }> {
        try {
            const qb = this.playerRepository
                .createQueryBuilder('player')
                .leftJoinAndSelect('player.detailedStatistics', 'stats')
                .where('player.leagueId = :league', { league })
                .andWhere('player.season = :season', { season })
                .andWhere('stats.goals->\'assists\' IS NOT NULL')
                .orderBy('CAST(stats.goals->>\'assists\' AS INTEGER)', 'DESC')
                .addOrderBy('player.name', 'ASC');

            const totalItems = await qb.getCount();
            const players = await qb
                .skip((page - 1) * limit)
                .take(limit)
                .getMany();

            this.logger.debug(`Found ${players.length} top assists in DB for league ${league} season ${season}`);
            return { players, totalItems };
        } catch (error) {
            this.logger.error(`Error getting top assists from DB: ${error.message}`);
            return { players: [], totalItems: 0 };
        }
    }

    /**
     * Fetch top scorers from API
     */
    private async fetchTopScorersFromApi(league: number, season: number): Promise<any[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topscorers`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };

                this.logger.debug(`Calling API: ${apiUrl} with params: league=${league}, season=${season}`);

                return axios.get(apiUrl, {
                    params: { league, season },
                    headers,
                });
            });

            this.logger.debug(`API response for top scorers league ${league} season ${season}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No top scorers data returned from API for league ${league} season ${season}`);
                return [];
            }

            return response.data.response;
        } catch (error) {
            this.logger.error(`Failed to fetch top scorers from API: ${error.message}`);
            throw error;
        }
    }

    /**
     * Fetch top assists from API
     */
    private async fetchTopAssistsFromApi(league: number, season: number): Promise<any[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topassists`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };

                this.logger.debug(`Calling API: ${apiUrl} with params: league=${league}, season=${season}`);

                return axios.get(apiUrl, {
                    params: { league, season },
                    headers,
                });
            });

            this.logger.debug(`API response for top assists league ${league} season ${season}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No top assists data returned from API for league ${league} season ${season}`);
                return [];
            }

            return response.data.response;
        } catch (error) {
            this.logger.error(`Failed to fetch top assists from API: ${error.message}`);
            throw error;
        }
    }

    /**
     * Save players and statistics to database with transaction
     */
    private async savePlayersAndStatistics(apiData: any[]): Promise<void> {
        try {
            this.logger.debug(`Starting to save ${apiData.length} players and statistics to database`);

            await this.dataSource.transaction(async (manager) => {
                const playerRepo = manager.getRepository(Player);
                const statsRepo = manager.getRepository(PlayerStatistics);
                const teamRepo = manager.getRepository(Team);
                const leagueRepo = manager.getRepository(League);

                // Process in batches to avoid memory issues
                const batchSize = 10;
                for (let i = 0; i < apiData.length; i += batchSize) {
                    const batch = apiData.slice(i, i + batchSize);
                    this.logger.debug(`Processing batch ${Math.floor(i / batchSize) + 1}: ${batch.length} players`);

                    for (const item of batch) {
                        const playerData = item.player;
                        const statistics = item.statistics || [];

                        if (!playerData?.id) {
                            this.logger.warn(`Invalid player data: ${JSON.stringify(playerData)}`);
                            continue;
                        }

                        // Process each statistic (player can have multiple teams/leagues)
                        for (const stat of statistics) {
                            if (!stat?.team?.id || !stat?.league?.id) {
                                this.logger.warn(`Invalid statistics data for player ${playerData.id}`);
                                continue;
                            }

                            // Upsert team
                            await teamRepo.upsert({
                                externalId: stat.team.id,
                                name: stat.team.name,
                                logo: stat.team.logo,
                                season: stat.league.season,
                                leagueId: stat.league.id,
                            }, ['externalId']);

                            // Upsert league
                            await leagueRepo.upsert({
                                externalId: stat.league.id,
                                name: stat.league.name,
                                season: stat.league.season,
                                country: stat.league.country,
                                logo: stat.league.logo,
                            }, ['externalId', 'season']);

                            // Upsert player
                            const playerEntity = {
                                externalId: playerData.id,
                                name: playerData.name,
                                firstName: playerData.firstname,
                                lastName: playerData.lastname,
                                age: playerData.age,
                                birthDate: playerData.birth?.date ? new Date(playerData.birth.date) : null,
                                birthPlace: playerData.birth?.place,
                                birthCountry: playerData.birth?.country,
                                nationality: playerData.nationality,
                                height: playerData.height,
                                weight: playerData.weight,
                                injured: playerData.injured || false,
                                photo: playerData.photo,
                                position: stat.games?.position,
                                teamId: stat.team.id,
                                teamName: stat.team.name,
                                leagueId: stat.league.id,
                                leagueName: stat.league.name,
                                season: stat.league.season,
                                timestamp: Date.now(),
                            };

                            await playerRepo.upsert(playerEntity, ['externalId']);

                            // Get the saved player to link statistics
                            const savedPlayer = await playerRepo.findOne({
                                where: { externalId: playerData.id }
                            });

                            if (savedPlayer) {
                                // Upsert player statistics
                                const statsEntity = {
                                    playerId: savedPlayer.id,
                                    teamId: stat.team.id,
                                    teamName: stat.team.name,
                                    teamLogo: stat.team.logo,
                                    leagueId: stat.league.id,
                                    leagueName: stat.league.name,
                                    leagueCountry: stat.league.country,
                                    leagueLogo: stat.league.logo,
                                    season: stat.league.season,
                                    games: stat.games,
                                    substitutes: stat.substitutes,
                                    shots: stat.shots,
                                    goals: stat.goals,
                                    passes: stat.passes,
                                    tackles: stat.tackles,
                                    duels: stat.duels,
                                    dribbles: stat.dribbles,
                                    fouls: stat.fouls,
                                    cards: stat.cards,
                                    penalty: stat.penalty,
                                };

                                await statsRepo.upsert(statsEntity, ['playerId', 'teamId', 'leagueId', 'season']);
                            }
                        }
                    }

                    this.logger.debug(`Completed batch ${Math.floor(i / batchSize) + 1}`);
                }
            });

            this.logger.debug(`Successfully saved ${apiData.length} players and statistics to database`);
        } catch (error) {
            this.logger.error(`Failed to save players and statistics: ${error.message}`);
            throw error;
        }
    }

    /**
     * Map players to response DTOs
     */
    private mapToResponseDto(players: Player[]): any[] {
        return players.map(player => ({
            player: {
                id: player.externalId,
                name: player.name,
                firstname: player.firstName,
                lastname: player.lastName,
                age: player.age,
                birth: {
                    date: player.birthDate?.toISOString().split('T')[0],
                    place: player.birthPlace,
                    country: player.birthCountry
                },
                nationality: player.nationality,
                height: player.height,
                weight: player.weight,
                injured: player.injured,
                photo: player.photo
            },
            statistics: [] // Will be populated from PlayerStatistics if needed
        }));
    }

    /**
     * Execute API call with retry logic
     */
    private async executeWithRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
        let lastError: Error = new Error('Unknown error');

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error as Error;
                this.logger.warn(`API call attempt ${attempt} failed: ${error.message}`);

                if (attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
                    this.logger.debug(`Retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError;
    }
}
