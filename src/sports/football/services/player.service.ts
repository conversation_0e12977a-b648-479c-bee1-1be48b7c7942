import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import axios from 'axios';

import { Player } from '../models/player.entity';
import { PlayerStatistics } from '../models/player-statistics.entity';
import {
    GetTopScorersDto,
    GetTopAssistsDto,
    GetPlayersDto,
    PaginatedPlayersResponse,
    PlayerResponseDto
} from '../models/player.dto';
import { CacheService } from '../../../core';

@Injectable()
export class PlayerService {
    private readonly logger = new Logger(PlayerService.name);

    constructor(
        @InjectRepository(Player)
        private readonly playerRepository: Repository<Player>,
        @InjectRepository(PlayerStatistics)
        private readonly playerStatisticsRepository: Repository<PlayerStatistics>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
    ) { }

    /**
     * Get top scorers for a league and season (simple implementation)
     */
    async getTopScorers(query: GetTopScorersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { league, season, page = 1, limit = 20 } = query;
            this.logger.debug(`Getting top scorers for league ${league}, season ${season}`);

            // For now, fetch from API and return directly (no DB save)
            const apiPlayers = await this.fetchTopScorersFromApi(league, season);

            // Apply pagination
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedPlayers = apiPlayers.slice(startIndex, endIndex);

            const result: PaginatedPlayersResponse = {
                data: paginatedPlayers,
                meta: {
                    totalItems: apiPlayers.length,
                    totalPages: Math.ceil(apiPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopScorers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Fetch top scorers from API
     */
    private async fetchTopScorersFromApi(league: number, season: number): Promise<any[]> {
        try {
            const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topscorers`;
            const apiKey = this.configService.get('app.apiFootballKey');

            this.logger.debug(`Fetching top scorers from API: ${apiUrl}?league=${league}&season=${season}`);

            const response = await axios.get(apiUrl, {
                headers: {
                    'x-apisports-key': apiKey,
                },
                params: {
                    league,
                    season,
                },
                timeout: 10000,
            });

            if (response.data && response.data.response) {
                this.logger.debug(`API returned ${response.data.response.length} top scorers`);
                return response.data.response;
            }

            this.logger.warn(`API returned no data for league ${league} season ${season}`);
            return [];
        } catch (error) {
            this.logger.error(`Error fetching top scorers from API: ${error.message}`);
            return [];
        }
    }

    /**
     * Get top assists for a league and season (simple implementation)
     */
    async getTopAssists(query: GetTopAssistsDto): Promise<PaginatedPlayersResponse> {
        try {
            const { league, season, page = 1, limit = 20 } = query;
            this.logger.debug(`Getting top assists for league ${league}, season ${season}`);

            // For now, fetch from API and return directly (no DB save)
            const apiPlayers = await this.fetchTopAssistsFromApi(league, season);

            // Apply pagination
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedPlayers = apiPlayers.slice(startIndex, endIndex);

            const result: PaginatedPlayersResponse = {
                data: paginatedPlayers,
                meta: {
                    totalItems: apiPlayers.length,
                    totalPages: Math.ceil(apiPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopAssists: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Fetch top assists from API
     */
    private async fetchTopAssistsFromApi(league: number, season: number): Promise<any[]> {
        try {
            const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topassists`;
            const apiKey = this.configService.get('app.apiFootballKey');

            this.logger.debug(`Fetching top assists from API: ${apiUrl}?league=${league}&season=${season}`);

            const response = await axios.get(apiUrl, {
                headers: {
                    'x-apisports-key': apiKey,
                },
                params: {
                    league,
                    season,
                },
                timeout: 10000,
            });

            if (response.data && response.data.response) {
                this.logger.debug(`API returned ${response.data.response.length} top assists`);
                return response.data.response;
            }

            this.logger.warn(`API returned no data for league ${league} season ${season}`);
            return [];
        } catch (error) {
            this.logger.error(`Error fetching top assists from API: ${error.message}`);
            return [];
        }
    }

    /**
     * Get player by external ID
     */
    async getPlayerById(externalId: number, season?: number): Promise<PlayerResponseDto> {
        try {
            const cacheKey = `player_${externalId}_${season || 'all'}`;
            this.logger.debug(`Getting player by externalId: ${externalId}, season: ${season}`);

            // Check cache first
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning player from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }

            // Try to get from database first
            let player = await this.getPlayerFromDb(externalId, season);

            // If not found in DB, fetch from API
            if (!player) {
                this.logger.debug(`Player ${externalId} not found in DB, fetching from API`);
                const apiPlayers = await this.fetchPlayerFromApi(externalId, season);

                if (apiPlayers.length > 0) {
                    // Save to database with transaction
                    await this.savePlayersAndStatistics(apiPlayers);

                    // Get from database again
                    player = await this.getPlayerFromDb(externalId, season);
                }
            }

            if (!player) {
                throw new NotFoundException(`Player with externalId ${externalId} not found`);
            }

            // Get player statistics
            const statistics = await this.getPlayerStatistics(externalId, season);

            const result = {
                player: {
                    id: player.externalId,
                    name: player.name,
                    firstname: player.firstName,
                    lastname: player.lastName,
                    age: player.age,
                    birth: {
                        date: player.birthDate?.toISOString().split('T')[0],
                        place: player.birthPlace,
                        country: player.birthCountry
                    },
                    nationality: player.nationality,
                    height: player.height,
                    weight: player.weight,
                    injured: player.injured,
                    photo: player.photo
                },
                statistics
            };

            // Cache the result for 30 minutes
            await this.cacheService.setCache(cacheKey, JSON.stringify(result), 1800);
            this.logger.debug(`Cached player for key: ${cacheKey}`);

            return result;
        } catch (error) {
            this.logger.error(`Error in getPlayerById: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Search players
     */
    async searchPlayers(query: GetPlayersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { team, league, season, search, page = 1, limit = 20 } = query;
            const cacheKey = `players_search_${team || ''}_${league || ''}_${season || ''}_${search || ''}_${page}_${limit}`;

            this.logger.debug(`Searching players with filters: team=${team}, league=${league}, season=${season}, search=${search}`);

            // Check cache first
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning players from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }

            // Build database query
            const qb = this.playerRepository.createQueryBuilder('player');

            if (search) {
                qb.andWhere('LOWER(player.name) LIKE LOWER(:search)', { search: `%${search}%` });
            }

            if (team) {
                qb.andWhere('player.teamId = :team', { team });
            }

            if (league) {
                qb.andWhere('player.leagueId = :league', { league });
            }

            if (season) {
                qb.andWhere('player.season = :season', { season });
            }

            // Get total count
            const totalItems = await qb.getCount();

            // Apply pagination and ordering
            const players = await qb
                .skip((page - 1) * limit)
                .take(limit)
                .orderBy('player.name', 'ASC')
                .getMany();

            const result: PaginatedPlayersResponse = {
                data: this.mapToResponseDtoSimple(players),
                meta: {
                    totalItems,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            // Cache the result for 15 minutes
            await this.cacheService.setCache(cacheKey, JSON.stringify(result), 900);
            this.logger.debug(`Cached players search for key: ${cacheKey}`);

            return result;
        } catch (error) {
            this.logger.error(`Error in searchPlayers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get top scorers from database
     */
    private async getTopScorersFromDb(league: number, season: number, page: number, limit: number): Promise<{ players: Player[], totalItems: number }> {
        try {
            // Query PlayerStatistics to get top scorers, then join with Player
            const statsQb = this.playerStatisticsRepository
                .createQueryBuilder('stats')
                .leftJoinAndSelect('stats.player', 'player')
                .where('stats.leagueId = :league', { league })
                .andWhere('stats.season = :season', { season })
                .andWhere('stats.goals->\'total\' IS NOT NULL')
                .andWhere('CAST(stats.goals->>\'total\' AS INTEGER) > 0')
                .orderBy('CAST(stats.goals->>\'total\' AS INTEGER)', 'DESC')
                .addOrderBy('player.name', 'ASC');

            const totalItems = await statsQb.getCount();
            const statsWithPlayers = await statsQb
                .skip((page - 1) * limit)
                .take(limit)
                .getMany();

            // Extract players from statistics
            const players = statsWithPlayers.map(stat => stat.player).filter(player => player);

            this.logger.debug(`Found ${players.length} top scorers in DB for league ${league} season ${season}`);
            return { players, totalItems };
        } catch (error) {
            this.logger.error(`Error getting top scorers from DB: ${error.message}`);
            return { players: [], totalItems: 0 };
        }
    }

    /**
     * Get top assists from database
     */
    private async getTopAssistsFromDb(league: number, season: number, page: number, limit: number): Promise<{ players: Player[], totalItems: number }> {
        try {
            const qb = this.playerRepository
                .createQueryBuilder('player')
                .leftJoinAndSelect('player.detailedStatistics', 'stats')
                .where('player.leagueId = :league', { league })
                .andWhere('player.season = :season', { season })
                .andWhere('stats.goals->\'assists\' IS NOT NULL')
                .orderBy('CAST(stats.goals->>\'assists\' AS INTEGER)', 'DESC')
                .addOrderBy('player.name', 'ASC');

            const totalItems = await qb.getCount();
            const players = await qb
                .skip((page - 1) * limit)
                .take(limit)
                .getMany();

            this.logger.debug(`Found ${players.length} top assists in DB for league ${league} season ${season}`);
            return { players, totalItems };
        } catch (error) {
            this.logger.error(`Error getting top assists from DB: ${error.message}`);
            return { players: [], totalItems: 0 };
        }
    }

    /**
     * Fetch top scorers from API
     */
    private async fetchTopScorersFromApi(league: number, season: number): Promise<any[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topscorers`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };

                this.logger.debug(`Calling API: ${apiUrl} with params: league=${league}, season=${season}`);

                return axios.get(apiUrl, {
                    params: { league, season },
                    headers,
                });
            });

            this.logger.debug(`API response for top scorers league ${league} season ${season}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No top scorers data returned from API for league ${league} season ${season}`);
                return [];
            }

            return response.data.response;
        } catch (error) {
            this.logger.error(`Failed to fetch top scorers from API: ${error.message}`);
            throw error;
        }
    }

    /**
     * Fetch top assists from API
     */
    private async fetchTopAssistsFromApi(league: number, season: number): Promise<any[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topassists`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };

                this.logger.debug(`Calling API: ${apiUrl} with params: league=${league}, season=${season}`);

                return axios.get(apiUrl, {
                    params: { league, season },
                    headers,
                });
            });

            this.logger.debug(`API response for top assists league ${league} season ${season}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No top assists data returned from API for league ${league} season ${season}`);
                return [];
            }

            return response.data.response;
        } catch (error) {
            this.logger.error(`Failed to fetch top assists from API: ${error.message}`);
            throw error;
        }
    }

    /**
     * Fetch single player from API
     */
    private async fetchPlayerFromApi(externalId: number, season?: number): Promise<any[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };

                const params: any = { id: externalId };
                if (season) {
                    params.season = season;
                }

                this.logger.debug(`Calling API: ${apiUrl} with params: ${JSON.stringify(params)}`);

                return axios.get(apiUrl, {
                    params,
                    headers,
                });
            });

            this.logger.debug(`API response for player ${externalId}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No player data returned from API for player ${externalId}`);
                return [];
            }

            return response.data.response;
        } catch (error) {
            this.logger.error(`Failed to fetch player from API: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get player from database
     */
    private async getPlayerFromDb(externalId: number, season?: number): Promise<Player | null> {
        try {
            const qb = this.playerRepository
                .createQueryBuilder('player')
                .where('player.externalId = :externalId', { externalId });

            if (season) {
                qb.andWhere('player.season = :season', { season });
            }

            const player = await qb.getOne();
            this.logger.debug(`Found player in DB: ${player ? 'Yes' : 'No'} for externalId ${externalId}`);

            return player;
        } catch (error) {
            this.logger.error(`Error getting player from DB: ${error.message}`);
            return null;
        }
    }

    /**
     * Get player statistics
     */
    private async getPlayerStatistics(externalId: number, season?: number): Promise<any[]> {
        try {
            const qb = this.playerStatisticsRepository
                .createQueryBuilder('stats')
                .leftJoin('stats.player', 'player')
                .where('player.externalId = :externalId', { externalId });

            if (season) {
                qb.andWhere('stats.season = :season', { season });
            }

            const statistics = await qb.getMany();

            return statistics.map(stat => ({
                team: {
                    id: stat.teamId,
                    name: stat.teamName,
                    logo: stat.teamLogo
                },
                league: {
                    id: stat.leagueId,
                    name: stat.leagueName,
                    country: stat.leagueCountry,
                    logo: stat.leagueLogo,
                    season: stat.season
                },
                games: stat.games,
                substitutes: stat.substitutes,
                shots: stat.shots,
                goals: stat.goals,
                passes: stat.passes,
                tackles: stat.tackles,
                duels: stat.duels,
                dribbles: stat.dribbles,
                fouls: stat.fouls,
                cards: stat.cards,
                penalty: stat.penalty
            }));
        } catch (error) {
            this.logger.error(`Error getting player statistics: ${error.message}`);
            return [];
        }
    }

    /**
     * Save players and statistics to database with transaction
     */
    private async savePlayersAndStatistics(apiData: any[]): Promise<void> {
        try {
            this.logger.debug(`Starting to save ${apiData.length} players and statistics to database`);

            await this.dataSource.transaction(async (manager) => {
                const playerRepo = manager.getRepository(Player);
                const statsRepo = manager.getRepository(PlayerStatistics);
                const teamRepo = manager.getRepository(Team);
                const leagueRepo = manager.getRepository(League);

                // Process in batches to avoid memory issues
                const batchSize = 10;
                for (let i = 0; i < apiData.length; i += batchSize) {
                    const batch = apiData.slice(i, i + batchSize);
                    this.logger.debug(`Processing batch ${Math.floor(i / batchSize) + 1}: ${batch.length} players`);

                    for (const item of batch) {
                        const playerData = item.player;
                        const statistics = item.statistics || [];

                        if (!playerData?.id) {
                            this.logger.warn(`Invalid player data: ${JSON.stringify(playerData)}`);
                            continue;
                        }

                        // Process each statistic (player can have multiple teams/leagues)
                        for (const stat of statistics) {
                            if (!stat?.team?.id || !stat?.league?.id) {
                                this.logger.warn(`Invalid statistics data for player ${playerData.id}`);
                                continue;
                            }

                            // Upsert team
                            await teamRepo.upsert({
                                externalId: stat.team.id,
                                name: stat.team.name,
                                logo: stat.team.logo,
                                season: stat.league.season,
                                leagueId: stat.league.id,
                            }, ['externalId']);

                            // Upsert league
                            await leagueRepo.upsert({
                                externalId: stat.league.id,
                                name: stat.league.name,
                                season: stat.league.season,
                                country: stat.league.country,
                                logo: stat.league.logo,
                            }, ['externalId', 'season']);

                            // Upsert player
                            const playerEntity = {
                                externalId: playerData.id,
                                name: playerData.name,
                                firstName: playerData.firstname,
                                lastName: playerData.lastname,
                                age: playerData.age,
                                birthDate: playerData.birth?.date ? new Date(playerData.birth.date) : null,
                                birthPlace: playerData.birth?.place,
                                birthCountry: playerData.birth?.country,
                                nationality: playerData.nationality,
                                height: playerData.height,
                                weight: playerData.weight,
                                injured: playerData.injured || false,
                                photo: playerData.photo,
                                position: stat.games?.position,
                                teamId: stat.team.id,
                                teamName: stat.team.name,
                                leagueId: stat.league.id,
                                leagueName: stat.league.name,
                                season: stat.league.season,
                                timestamp: Date.now(),
                            };

                            await playerRepo.upsert(playerEntity, ['externalId']);

                            // Get the saved player to link statistics
                            const savedPlayer = await playerRepo.findOne({
                                where: { externalId: playerData.id }
                            });

                            if (savedPlayer) {
                                // Upsert player statistics
                                const statsEntity = {
                                    playerId: savedPlayer.id,
                                    teamId: stat.team.id,
                                    teamName: stat.team.name,
                                    teamLogo: stat.team.logo,
                                    leagueId: stat.league.id,
                                    leagueName: stat.league.name,
                                    leagueCountry: stat.league.country,
                                    leagueLogo: stat.league.logo,
                                    season: stat.league.season,
                                    games: stat.games,
                                    substitutes: stat.substitutes,
                                    shots: stat.shots,
                                    goals: stat.goals,
                                    passes: stat.passes,
                                    tackles: stat.tackles,
                                    duels: stat.duels,
                                    dribbles: stat.dribbles,
                                    fouls: stat.fouls,
                                    cards: stat.cards,
                                    penalty: stat.penalty,
                                };

                                await statsRepo.upsert(statsEntity, ['playerId', 'teamId', 'leagueId', 'season']);
                            }
                        }
                    }

                    this.logger.debug(`Completed batch ${Math.floor(i / batchSize) + 1}`);
                }
            });

            this.logger.debug(`Successfully saved ${apiData.length} players and statistics to database`);
        } catch (error) {
            this.logger.error(`Failed to save players and statistics: ${error.message}`);
            throw error;
        }
    }

    /**
     * Map players to response DTOs
     */
    private async mapToResponseDto(players: Player[]): Promise<any[]> {
        const result = [];

        for (const player of players) {
            // Get statistics for each player
            const statistics = await this.getPlayerStatistics(player.externalId);

            result.push({
                player: {
                    id: player.externalId,
                    name: player.name,
                    firstname: player.firstName,
                    lastname: player.lastName,
                    age: player.age,
                    birth: {
                        date: player.birthDate?.toISOString().split('T')[0],
                        place: player.birthPlace,
                        country: player.birthCountry
                    },
                    nationality: player.nationality,
                    height: player.height,
                    weight: player.weight,
                    injured: player.injured,
                    photo: player.photo
                },
                statistics
            });
        }

        return result;
    }

    /**
     * Map players to response DTOs (without statistics for performance)
     */
    private mapToResponseDtoSimple(players: Player[]): any[] {
        return players.map(player => ({
            player: {
                id: player.externalId,
                name: player.name,
                firstname: player.firstName,
                lastname: player.lastName,
                age: player.age,
                birth: {
                    date: player.birthDate?.toISOString().split('T')[0],
                    place: player.birthPlace,
                    country: player.birthCountry
                },
                nationality: player.nationality,
                height: player.height,
                weight: player.weight,
                injured: player.injured,
                photo: player.photo
            },
            statistics: [] // Empty for performance
        }));
    }

    /**
     * Execute API call with retry logic
     */
    private async executeWithRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
        let lastError: Error = new Error('Unknown error');

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error as Error;
                this.logger.warn(`API call attempt ${attempt} failed: ${error.message}`);

                if (attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
                    this.logger.debug(`Retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError;
    }
}
