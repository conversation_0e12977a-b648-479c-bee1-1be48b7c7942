import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Between, Raw } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { CacheService } from '../../../core';
import { UtilsService, ImageService } from '../../../shared';
import { Fixture } from '../models/fixture.entity';
import { League } from '../models/league.entity';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';



@Injectable()
export class SyncService {
    private readonly logger = new Logger(SyncService.name);
    private readonly BATCH_SIZE = 100;
    private readonly MAX_IDS_PER_REQUEST = 20;
    private readonly MAX_CONCURRENT_REQUESTS = 1;
    private readonly LEAGUE_BATCH_SIZE = 10; // Số leagues xử lý cùng lúc

    constructor(
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        @InjectRepository(League)
        private readonly leagueRepository: Repository<League>,
        private readonly cacheService: CacheService,
        private readonly configService: ConfigService,
        private readonly utilsService: UtilsService,
        private readonly imageService: ImageService,
        @InjectQueue('sync-queue') private readonly syncQueue: Queue,
    ) { }

    @Cron(CronExpression.EVERY_10_SECONDS, { utcOffset: 0 })
    async syncLiveFixtures() {
        this.logger.debug('Starting smart live fixtures sync');

        try {
            // Bước 1: Lấy fixtures cần sync (smart filtering)
            const fixtures = await this.getFixturesNeedingSync();

            if (fixtures.length === 0) {
                this.logger.debug('No fixtures need sync at this time');
                return;
            }

            // Bước 2: Phân loại fixtures theo priority và time
            const categorized = this.categorizeFixturesByTime(fixtures);

            // Bước 3: Sync theo priority với time-based logic
            await this.syncFixturesByPriority(categorized);

        } catch (error) {
            this.logger.error(`Live sync failed: ${error.message}`);
            // Fallback strategy nếu cần
        }
    }

    /**
     * Get fixtures that need sync based on smart time filtering
     * Chỉ lấy fixtures trong time window cần sync để optimize performance
     */
    private async getFixturesNeedingSync(): Promise<Fixture[]> {
        const now = this.utilsService.getUtcNow();

        // Lấy active leagues
        const activeLeagues = await this.leagueRepository.find({
            where: { active: true },
            select: ['externalId']
        });
        const leagueIds = activeLeagues.map(league => league.externalId);

        if (leagueIds.length === 0) {
            this.logger.debug('No active leagues found');
            return [];
        }

        // Time window: 2 giờ trước đến 30 phút sau hiện tại
        const startTime = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 giờ trước
        const endTime = new Date(now.getTime() + 30 * 60 * 1000);       // 30 phút sau

        const fixtures = await this.fixtureRepository.find({
            where: {
                date: Between(startTime, endTime),
                leagueId: In(leagueIds),
                // Chỉ lấy fixtures chưa finished
                data: Raw(alias => `${alias} ->> 'status' NOT IN ('FT', 'CANC', 'AWD', 'PST')`)
            }
        });

        this.logger.debug(`Found ${fixtures.length} fixtures needing sync in time window ${startTime.toISOString()} - ${endTime.toISOString()}`);
        return fixtures;
    }

    /**
     * Categorize fixtures by time-based priority
     * Phân loại fixtures theo thời gian để xử lý theo priority
     */
    private categorizeFixturesByTime(fixtures: Fixture[]) {
        const needsApiData: Fixture[] = [];      // ≤ 0 phút: Cần API data
        const needsTimeUpdate: Fixture[] = [];   // 5-10 phút: Cần time-based status update
        const monitoring: Fixture[] = [];        // Khác: Chỉ monitor

        for (const fixture of fixtures) {
            const minutesUntilMatch = this.utilsService.getMinutesDifference(fixture.date);

            if (minutesUntilMatch <= 0) {
                // Trận đã bắt đầu hoặc đang diễn ra → Cần API data
                needsApiData.push(fixture);
            } else if (minutesUntilMatch <= 10 && minutesUntilMatch > 0) {
                // Trận trong 10 phút tới → Cần time-based status update
                needsTimeUpdate.push(fixture);
            } else {
                // Trận còn xa → Chỉ monitor
                monitoring.push(fixture);
            }
        }

        this.logger.debug(`Categorized fixtures: ${needsApiData.length} need API, ${needsTimeUpdate.length} need time update, ${monitoring.length} monitoring`);

        return {
            needsApiData,
            needsTimeUpdate,
            monitoring
        };
    }

    /**
     * Sync fixtures by priority với time-based logic
     */
    private async syncFixturesByPriority(categorized: any) {
        const { needsApiData, needsTimeUpdate, monitoring } = categorized;

        // Priority 1: Fixtures cần API data (đang diễn ra)
        if (needsApiData.length > 0) {
            await this.syncFixturesWithApi(needsApiData);
        }

        // Priority 2: Fixtures cần time-based status update
        if (needsTimeUpdate.length > 0) {
            await this.updateFixturesTimeBasedStatus(needsTimeUpdate);
        }

        // Priority 3: Monitoring fixtures (log only)
        if (monitoring.length > 0) {
            this.logger.debug(`Monitoring ${monitoring.length} fixtures not in sync window`);
        }
    }

    /**
     * Sync fixtures with API data (cho fixtures đang diễn ra)
     */
    private async syncFixturesWithApi(fixtures: Fixture[]) {
        try {
            // Lấy API data cho fixtures
            const externalIds = fixtures.map(f => f.externalId);
            const apiDataMap = await this.fetchFixturesFromApi(externalIds);

            const updates: Fixture[] = [];

            for (const fixture of fixtures) {
                const apiData = apiDataMap[fixture.externalId];

                if (apiData) {
                    const hasChanges = this.updateFixtureFromApiData(fixture, apiData);
                    if (hasChanges) {
                        updates.push(fixture);
                    }
                } else {
                    this.logger.warn(`No API data for fixture ${fixture.externalId}`);
                }
            }

            // Batch update changed fixtures
            if (updates.length > 0) {
                await this.fixtureRepository.save(updates);
                this.logger.log(`Updated ${updates.length} fixtures from API data`);

                // Clear cache sau khi update
                await this.cacheService.deleteByPattern('fixtures_list_*');
                await this.cacheService.deleteByPattern('team_schedule_*');
            }

        } catch (error) {
            this.logger.error(`Failed to sync fixtures with API: ${error.message}`);
        }
    }

    /**
     * Update fixtures với time-based status logic
     */
    private async updateFixturesTimeBasedStatus(fixtures: Fixture[]) {
        const updates: Fixture[] = [];

        for (const fixture of fixtures) {
            const hasChanges = this.updateFixtureTimeBasedStatus(fixture);
            if (hasChanges) {
                updates.push(fixture);
            }
        }

        if (updates.length > 0) {
            await this.fixtureRepository.save(updates);
            this.logger.log(`Updated ${updates.length} fixtures with time-based status`);

            // Clear cache sau khi update
            await this.cacheService.deleteByPattern('fixtures_list_*');
            await this.cacheService.deleteByPattern('team_schedule_*');
        }
    }

    /**
     * Update fixture status dựa trên thời gian (time-based logic)
     */
    private updateFixtureTimeBasedStatus(fixture: Fixture): boolean {
        const minutesUntilMatch = this.utilsService.getMinutesDifference(fixture.date);
        let hasChanges = false;

        if (minutesUntilMatch > 10) {
            // Hơn 10 phút → Không thay đổi status (thường là 'NS')
            this.logger.debug(`Fixture ${fixture.externalId}: ${minutesUntilMatch} minutes until match, keeping status ${fixture.data.status}`);

        } else if (minutesUntilMatch <= 10 && minutesUntilMatch > 5) {
            // 10-5 phút → UPCOMING
            if (fixture.data.status !== 'UPCOMING') {
                fixture.data.status = 'UPCOMING';
                fixture.data.statusLong = 'Upcoming';
                hasChanges = true;
                this.logger.debug(`Fixture ${fixture.externalId}: Set to UPCOMING (${minutesUntilMatch} minutes)`);
            }

        } else if (minutesUntilMatch <= 5 && minutesUntilMatch > 0) {
            // 5-0 phút → LIVE
            if (fixture.data.status !== 'LIVE') {
                fixture.data.status = 'LIVE';
                fixture.data.statusLong = 'Live';
                hasChanges = true;
                this.logger.debug(`Fixture ${fixture.externalId}: Set to LIVE (${minutesUntilMatch} minutes)`);
            }
        }

        return hasChanges;
    }

    /**
     * Update fixture từ API data
     */
    private updateFixtureFromApiData(fixture: Fixture, apiData: any): boolean {
        let hasChanges = false;

        // Update status nếu có thay đổi
        if (fixture.data.status !== apiData.fixture.status.short) {
            fixture.data.status = apiData.fixture.status.short;
            fixture.data.statusLong = apiData.fixture.status.long;
            fixture.data.elapsed = apiData.fixture.status.elapsed || 0;
            hasChanges = true;
            this.logger.debug(`Fixture ${fixture.externalId}: Updated status from API to ${fixture.data.status}`);
        }

        // Update scores nếu có thay đổi
        const newGoalsHome = apiData.goals?.home ?? 0;
        const newGoalsAway = apiData.goals?.away ?? 0;

        if (fixture.data.goalsHome !== newGoalsHome || fixture.data.goalsAway !== newGoalsAway) {
            fixture.data.goalsHome = newGoalsHome;
            fixture.data.goalsAway = newGoalsAway;
            hasChanges = true;
            this.logger.debug(`Fixture ${fixture.externalId}: Updated score to ${newGoalsHome}-${newGoalsAway}`);
        }

        // Update other score details
        if (apiData.score) {
            fixture.data.scoreHalftimeHome = apiData.score.halftime?.home ?? 0;
            fixture.data.scoreHalftimeAway = apiData.score.halftime?.away ?? 0;
            fixture.data.scoreFulltimeHome = apiData.score.fulltime?.home ?? 0;
            fixture.data.scoreFulltimeAway = apiData.score.fulltime?.away ?? 0;
        }

        return hasChanges;
    }

    /**
     * Daily sync all active league fixtures
     * Runs at 2:00 AM UTC every day
     */
    @Cron('0 2 * * *', { utcOffset: 0 })
    async syncAllLeagueFixtures() {
        this.logger.log('Starting daily sync of all active league fixtures');

        try {
            // Bước 1: Lấy danh sách active leagues
            const activeLeagues = await this.leagueRepository.find({
                where: { active: true },
                select: ['externalId', 'season'],
            });

            if (activeLeagues.length === 0) {
                this.logger.warn('No active leagues found for daily sync');
                return;
            }

            this.logger.log(`Found ${activeLeagues.length} active leagues to sync`);

            // Bước 2: Chia leagues thành batches
            const leagueBatches = [];
            for (let i = 0; i < activeLeagues.length; i += this.LEAGUE_BATCH_SIZE) {
                leagueBatches.push(activeLeagues.slice(i, i + this.LEAGUE_BATCH_SIZE));
            }

            this.logger.log(`Processing ${leagueBatches.length} league batches`);

            // Bước 3: Xử lý từng batch tuần tự với detailed tracking
            const startTime = this.utilsService.getUtcNow();
            let totalFixturesProcessed = 0;
            let totalFixturesUpserted = 0;
            let processedLeagues = 0;
            const errors: string[] = [];

            for (let batchIndex = 0; batchIndex < leagueBatches.length; batchIndex++) {
                const batch = leagueBatches[batchIndex];
                this.logger.log(`Processing league batch ${batchIndex + 1}/${leagueBatches.length} with ${batch.length} leagues`);

                try {
                    // Fetch fixtures cho tất cả leagues trong batch song song
                    const batchResults = await Promise.all(
                        batch.map(league => this.fetchLeagueFixtures(league.externalId, league.season))
                    );

                    // Flatten và filter valid fixtures
                    const batchFixtures = batchResults.flat().filter((fixture): fixture is Fixture => fixture !== null);
                    totalFixturesProcessed += batchFixtures.length;

                    if (batchFixtures.length > 0) {
                        // Smart upsert fixtures với protection
                        const actualUpserted = await this.upsertFixturesBatch(batchFixtures);
                        totalFixturesUpserted += actualUpserted;
                        processedLeagues += batch.length;

                        this.logger.log(`Batch ${batchIndex + 1}: Smart upserted ${actualUpserted}/${batchFixtures.length} fixtures from ${batch.length} leagues`);
                    } else {
                        this.logger.log(`Batch ${batchIndex + 1}: No fixtures to process from ${batch.length} leagues`);
                        processedLeagues += batch.length;
                    }

                    // Delay giữa các batches để tránh overload API
                    if (batchIndex < leagueBatches.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
                    }

                } catch (error) {
                    this.logger.error(`Failed to process league batch ${batchIndex + 1}: ${error.message}`);
                    errors.push(`Batch ${batchIndex + 1}: ${error.message}`);
                    // Continue với batch tiếp theo
                }
            }

            // Bước 4: Clear cache sau khi sync xong
            await this.cacheService.deleteByPattern('fixtures_list_*');
            await this.cacheService.deleteByPattern('team_schedule_*');

            // Calculate duration và final stats
            const endTime = this.utilsService.getUtcNow();
            const duration = Math.round((endTime.getTime() - startTime.getTime()) / 1000);

            this.logger.log(`Daily sync completed: ${processedLeagues}/${activeLeagues.length} leagues, ${totalFixturesUpserted}/${totalFixturesProcessed} fixtures upserted, ${duration}s duration`);

            if (errors.length > 0) {
                this.logger.warn(`Daily sync had ${errors.length} errors: ${errors.slice(0, 3).join('; ')}${errors.length > 3 ? '...' : ''}`);
            }

        } catch (error) {
            this.logger.error(`Daily league fixtures sync failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Manual trigger for daily sync (for testing)
     */
    async triggerDailySync(): Promise<{ success: boolean; message: string; stats?: any }> {
        try {
            this.logger.log('Manual trigger: Starting daily sync of all active league fixtures');
            await this.syncAllLeagueFixtures();
            return {
                success: true,
                message: 'Daily sync completed successfully'
            };
        } catch (error) {
            this.logger.error(`Manual daily sync failed: ${error.message}`);
            return {
                success: false,
                message: `Daily sync failed: ${error.message}`
            };
        }
    }

    /**
     * Legacy method - kept for backward compatibility
     * New live sync logic doesn't use queue anymore
     */
    async processFixtureBatch(job: { data: { fixtures: Fixture[] } }) {
        this.logger.debug(`Legacy processFixtureBatch called - consider using new syncLiveFixtures logic`);

        // For now, just log that this method was called
        // In future, this can be removed entirely
        const { fixtures } = job.data;
        this.logger.debug(`Legacy batch processing ${fixtures.length} fixtures`);
    }

    /**
     * Fetch fixtures for a specific league and season
     * @param leagueId - League external ID
     * @param season - Season year
     * @returns Array of fixtures
     */
    private async fetchLeagueFixtures(leagueId: number, season: number): Promise<Fixture[]> {
        try {
            const response = await axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures`, {
                params: {
                    league: leagueId,
                    season,
                    timezone: 'UTC',
                },
                headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
            });

            if (!response.data || !Array.isArray(response.data.response) || response.data.response.length === 0) {
                this.logger.debug(`No fixtures returned from API for league ${leagueId}, season ${season}`);
                return [];
            }

            const fixtures: Fixture[] = await Promise.all(
                response.data.response.map(async (apiData: any) => {
                    const fixture = new Fixture();
                    fixture.externalId = apiData.fixture.id;
                    fixture.leagueId = apiData.league.id;
                    fixture.leagueName = apiData.league.name || 'Unknown';
                    fixture.season = apiData.league.season || 0;
                    fixture.round = apiData.league.round || '';
                    fixture.homeTeamId = apiData.teams.home.id;
                    fixture.awayTeamId = apiData.teams.away.id;
                    fixture.slug = this.utilsService.generateSlug(
                        `${apiData.teams.home.name || 'home'}-vs-${apiData.teams.away.name || 'away'}`,
                        this.utilsService.formatDate(new Date(apiData.fixture.date)),
                    );
                    fixture.date = this.utilsService.parseUtcDate(apiData.fixture.date);
                    fixture.venueId = apiData.fixture.venue?.id || 0;
                    fixture.venueName = apiData.fixture.venue?.name || '';
                    fixture.venueCity = apiData.fixture.venue?.city || '';
                    fixture.referee = apiData.fixture.referee || '';
                    fixture.source = 'api';
                    fixture.createdBy = null;
                    fixture.timestamp = apiData.fixture.timestamp || Math.floor(Date.now() / 1000);
                    fixture.isHot = false;

                    // Download team logos
                    const homeTeamLogoPath = apiData.teams.home.logo
                        ? await this.downloadTeamLogo(apiData.teams.home.logo, apiData.teams.home.id)
                        : '';
                    const awayTeamLogoPath = apiData.teams.away.logo
                        ? await this.downloadTeamLogo(apiData.teams.away.logo, apiData.teams.away.id)
                        : '';

                    fixture.data = {
                        homeTeamName: apiData.teams.home.name || 'Unknown',
                        homeTeamLogo: homeTeamLogoPath || apiData.teams.home.logo || '',
                        awayTeamName: apiData.teams.away.name || 'Unknown',
                        awayTeamLogo: awayTeamLogoPath || apiData.teams.away.logo || '',
                        status: apiData.fixture.status?.short || 'NS',
                        statusLong: apiData.fixture.status?.long || 'Not Started',
                        statusExtra: apiData.fixture.status?.extra || 0,
                        elapsed: apiData.fixture.status?.elapsed || 0,
                        goalsHome: apiData.goals?.home ?? 0,
                        goalsAway: apiData.goals?.away ?? 0,
                        scoreHalftimeHome: apiData.score?.halftime?.home ?? 0,
                        scoreHalftimeAway: apiData.score?.halftime?.away ?? 0,
                        scoreFulltimeHome: apiData.score?.fulltime?.home ?? 0,
                        scoreFulltimeAway: apiData.score?.fulltime?.away ?? 0,
                        periods: {
                            first: apiData.fixture.periods?.first || 0,
                            second: apiData.fixture.periods?.second || 0,
                        },
                    };
                    return fixture;
                })
            );

            this.logger.debug(`Fetched ${fixtures.length} fixtures for league ${leagueId}, season ${season}`);
            return fixtures;
        } catch (error) {
            this.logger.error(`Failed to fetch fixtures for league ${leagueId}, season ${season}: ${error.message}, Status: ${error.response?.status || 'unknown'}`);
            return [];
        }
    }

    /**
     * Check if fixture is far enough in the future to be safe for upsert (UTC-safe)
     * @param fixtureDate - Fixture date
     * @param bufferMinutes - Buffer time in minutes (default: 5)
     * @returns True if fixture is safe to upsert, false if should be skipped
     */
    private isFixtureSafeForDailySync(fixtureDate: Date, bufferMinutes: number = 5): boolean {
        // Use UTC-safe time calculation
        const minutesDiff = this.utilsService.getMinutesDifference(fixtureDate);

        // Only safe if fixture is more than 5 minutes in the future
        // This leverages the fact that syncLiveFixtures runs every 10s
        // so any fixture within 5 minutes will have accurate live status
        return minutesDiff > bufferMinutes;
    }

    /**
     * Pure time-based smart upsert that leverages live sync system
     * @param fixtures - Fixtures to upsert
     * @returns Number of fixtures actually upserted
     */
    private async smartUpsertFixtures(fixtures: Fixture[]): Promise<number> {
        if (fixtures.length === 0) return 0;

        try {
            // Pure time-based filtering - no database queries needed
            // Trust that syncLiveFixtures (every 10s) maintains accurate status
            const safeFixtures: Fixture[] = [];
            const skippedFixtures: Fixture[] = [];

            for (const fixture of fixtures) {
                if (this.isFixtureSafeForDailySync(fixture.date)) {
                    // Fixture is >5 minutes in future, safe to upsert
                    // syncLiveFixtures will handle any status changes as match approaches
                    safeFixtures.push(fixture);
                } else {
                    // Fixture is within 5 minutes or in past
                    // Skip to avoid interfering with live sync system
                    skippedFixtures.push(fixture);
                }
            }

            this.logger.debug(`Pure time-based filtering: ${safeFixtures.length} safe fixtures (>5 min), ${skippedFixtures.length} skipped fixtures (≤5 min)`);

            let totalUpserted = 0;

            // Direct upsert for safe fixtures (no protection check needed)
            if (safeFixtures.length > 0) {
                await this.fixtureRepository.upsert(safeFixtures, ['externalId']);
                totalUpserted += safeFixtures.length;
                this.logger.debug(`Direct upserted ${safeFixtures.length} safe fixtures (>5 min in future)`);
            }

            // Log skipped fixtures for monitoring
            if (skippedFixtures.length > 0) {
                this.logger.log(`Skipped ${skippedFixtures.length} fixtures within 5 minutes (protected by live sync system)`);

                // Optional: Log some examples for debugging
                const exampleSkipped = skippedFixtures.slice(0, 3).map(f => ({
                    externalId: f.externalId,
                    date: f.date.toISOString(),
                    minutesFromNow: this.utilsService.getMinutesDifference(f.date)
                }));

                if (exampleSkipped.length > 0) {
                    this.logger.debug(`Example skipped fixtures: ${JSON.stringify(exampleSkipped)}`);
                }
            }

            // Summary statistics
            this.logger.log(`Time-based protection summary: ${totalUpserted} upserted, ${skippedFixtures.length} protected by live sync system`);

            return totalUpserted;

        } catch (error) {
            this.logger.error(`Smart upsert failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Smart upsert fixtures in batches with live/upcoming protection
     * @param fixtures - Array of fixtures to upsert
     * @returns Number of fixtures actually upserted
     */
    private async upsertFixturesBatch(fixtures: Fixture[]): Promise<number> {
        if (fixtures.length === 0) return 0;

        try {
            // Chia fixtures thành batches nhỏ hơn để tránh timeout
            const batches = [];
            for (let i = 0; i < fixtures.length; i += this.BATCH_SIZE) {
                batches.push(fixtures.slice(i, i + this.BATCH_SIZE));
            }

            // Smart upsert từng batch song song với protection
            const upsertResults = await Promise.all(
                batches.map(async (batch, index) => {
                    try {
                        const actualUpserted = await this.smartUpsertFixtures(batch);
                        this.logger.debug(`Smart upserted batch ${index + 1}/${batches.length}: ${actualUpserted}/${batch.length} fixtures`);
                        return actualUpserted;
                    } catch (error) {
                        this.logger.error(`Failed to smart upsert batch ${index + 1}: ${error.message}`);
                        return 0;
                    }
                })
            );

            const totalUpserted = upsertResults.reduce((sum: number, count: number) => sum + count, 0);
            this.logger.debug(`Smart upserted ${totalUpserted}/${fixtures.length} fixtures in ${batches.length} batches (protected live/upcoming)`);

            return totalUpserted;
        } catch (error) {
            this.logger.error(`Failed to smart upsert fixtures batch: ${error.message}`);
            throw error;
        }
    }


    private async fetchFixturesFromApi(externalIds: number[]): Promise<{ [key: number]: any }> {
        try {
            const idGroups: number[][] = [];
            for (let i = 0; i < externalIds.length; i += this.MAX_IDS_PER_REQUEST) {
                idGroups.push(externalIds.slice(i, i + this.MAX_IDS_PER_REQUEST));
            }

            const responses = await Promise.all(
                idGroups.map(async (group) => {
                    const idsString = group.join('-');
                    const response = await axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures`, {
                        params: { ids: idsString, timezone: 'UTC' },
                        headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
                    });
                    return response.data.response || [];
                }),
            );

            const apiDataMap: { [key: number]: any } = {};
            responses.flat().forEach((data: any) => {
                if (data.fixture && data.fixture.id) {
                    apiDataMap[data.fixture.id] = data;
                }
            });

            return apiDataMap;
        } catch (error) {
            this.logger.error(`Failed to fetch fixtures for IDs ${externalIds.join(', ')}: ${error.message}`);
            return [];
        }
    }

    /**
     * Download team logo with error handling
     * @param logoUrl - URL of the team logo
     * @param teamId - Team ID for filename
     * @returns Local file path or empty string if failed
     */
    private async downloadTeamLogo(logoUrl: string, teamId: number): Promise<string> {
        try {
            return await this.imageService.downloadImage(logoUrl, 'teams', `${teamId}.png`);
        } catch (error) {
            this.logger.warn(`Failed to download team logo for team ${teamId}: ${error.message}`);
            return '';
        }
    }

}