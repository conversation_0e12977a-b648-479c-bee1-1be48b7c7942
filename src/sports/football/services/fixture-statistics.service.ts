import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { FixtureStatistics } from '../models/fixture-statistics.entity';
import { FixtureStatisticsDto } from '../models/fixture-statistics.dto';
import { Fixture } from '../models/fixture.entity';
import { GetFixturesDto, PaginatedFixturesResponse, FixtureStatus } from '../models/fixture.dto';
import { FixtureService } from './fixture.service';
import { CacheService } from '../../../core';

@Injectable()
export class FixtureStatisticsService {
    private readonly logger = new Logger(FixtureStatisticsService.name);

    constructor(
        @InjectRepository(FixtureStatistics)
        private readonly statsRepository: Repository<FixtureStatistics>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        private readonly fixtureService: FixtureService,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
    ) { }

    /**
     * Get statistics for a fixture by external ID
     * @param externalId - Fixture external ID
     * @returns Fixture statistics
     */
    async getStatistics(externalId: number): Promise<{ data: FixtureStatisticsDto[]; status: number; message?: string }> {
        const cacheKey = `fixture_stats_${externalId}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning statistics from cache for key: ${cacheKey}`);
            return { data: JSON.parse(cached), status: 200 };
        }

        let stats = await this.statsRepository.find({ where: { fixtureId: externalId } });
        if (stats.length === 0) {
            this.logger.debug(`No statistics found in DB for fixture ${externalId}, fetching from API`);
            stats = await this.fetchFromApi(externalId);
            if (stats.length > 0) {
                await this.statsRepository.save(stats);
                this.logger.debug(`Saved ${stats.length} statistics to DB`);
            }
        }

        const response = stats.map(stat => ({
            fixtureId: stat.fixtureId,
            teamName: stat.teamName,
            statistics: stat.statistics,
        }));

        if (response.length === 0) {
            this.logger.warn(`No statistics available for fixture ${externalId}`);
            return { data: [], status: 200, message: `No statistics available for fixture ${externalId}` };
        }

        await this.cacheService.setCache(cacheKey, JSON.stringify(response), 3600);
        return { data: response, status: 200 };
    }

    /**
     * Get upcoming and live fixtures
     * @param query - Query parameters (league, season, team, date, page, limit)
     * @returns Paginated list of upcoming and live fixtures
     */
    async getUpcomingAndLiveFixtures(query: GetFixturesDto): Promise<PaginatedFixturesResponse> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const skip = (page - 1) * limit;

        const qb = this.fixtureRepository.createQueryBuilder('fixture')
            .where('fixture.data->>\'status\' IN (:...statuses)', {
                statuses: [
                    FixtureStatus.Upcoming,
                    FixtureStatus.Live,
                    FixtureStatus.FirstHalf,
                    FixtureStatus.Halftime,
                    FixtureStatus.SecondHalf,
                    FixtureStatus.ExtraTime,
                    FixtureStatus.Penalty,
                ],
            });

        if (query.league) {
            qb.andWhere('fixture.leagueId = :league', { league: query.league });
        }
        if (query.season) {
            qb.andWhere('fixture.season = :season', { season: query.season });
        }
        if (query.team) {
            qb.andWhere('fixture.homeTeamId = :team OR fixture.awayTeamId = :team', { team: query.team });
        }
        if (query.date) {
            const startOfDay = new Date(query.date);
            const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1);
            qb.andWhere('fixture.date BETWEEN :startOfDay AND :endOfDay', { startOfDay, endOfDay });
        }
        qb.orderBy('fixture.date', 'ASC'); // Default order by date
        const [fixtures, totalItems] = await qb
            .skip(skip)
            .take(limit)
            .getManyAndCount();

        const response: PaginatedFixturesResponse = {
            data: this.fixtureService.mapToResponseDto(fixtures),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        return response;
    }

    /**
     * Fetch statistics from external API with retry
     * @param externalId - Fixture external ID
     * @returns List of statistics
     */
    private async fetchFromApi(externalId: number): Promise<FixtureStatistics[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                return axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures/statistics`, {
                    params: { fixture: externalId },
                    headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
                });
            });

            if (response.data.errors && Object.keys(response.data.errors).length > 0) {
                this.logger.error(`API returned errors for fixture ${externalId}: ${JSON.stringify(response.data.errors)}`);
                return [];
            }

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No statistics data returned from API for fixture ${externalId}`);
                return [];
            }

            return response.data.response.map((data: any) => {
                const stat = new FixtureStatistics();
                stat.fixtureId = externalId;
                stat.teamName = data.team.name;
                stat.statistics = {
                    shotsOnGoal: data.statistics.find((s: any) => s.type === 'Shots on Goal')?.value || 0,
                    shotsOffGoal: data.statistics.find((s: any) => s.type === 'Shots off Goal')?.value || 0,
                    totalShots: data.statistics.find((s: any) => s.type === 'Total Shots')?.value || 0,
                    corners: data.statistics.find((s: any) => s.type === 'Corner Kicks')?.value || 0,
                    offsides: data.statistics.find((s: any) => s.type === 'Offsides')?.value || 0,
                    yellowCards: data.statistics.find((s: any) => s.type === 'Yellow Cards')?.value || 0,
                    redCards: data.statistics.find((s: any) => s.type === 'Red Cards')?.value || 0,
                    possession: data.statistics.find((s: any) => s.type === 'Ball Possession')?.value || '0%',
                };
                return stat;
            });
        } catch (error) {
            this.logger.error(`Failed to fetch statistics from API for fixture ${externalId}: ${error.message}`);
            return [];
        }
    }

    /**
     * Execute a function with retry logic
     * @param fn - Function to execute
     * @param retries - Number of retries
     * @param delay - Delay between retries (ms)
     * @returns Result of the function
     */
    private async executeWithRetry<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                if (attempt === retries) {
                    this.logger.error(`Failed after ${retries} attempts: ${error.message}`);
                    throw error;
                }
                this.logger.warn(`Attempt ${attempt} failed: ${error.message}. Retrying after ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        throw new Error('Unexpected error in retry logic');
    }
}