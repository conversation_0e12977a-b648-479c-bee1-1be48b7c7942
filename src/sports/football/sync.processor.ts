import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { SyncService } from './services/sync.service';
import { Logger } from '@nestjs/common';

@Processor('sync-queue')
export class SyncProcessor {
    private readonly logger = new Logger(SyncProcessor.name);

    constructor(private readonly syncService: SyncService) { }

    @Process('sync-fixtures')
    async handleSyncFixtures(job: Job<{ fixtures: any[] }>) {
        this.logger.debug(`Processing sync-fixtures job for ${job.data.fixtures.length} fixtures`);
        await this.syncService.processFixtureBatch(job);
    }
}