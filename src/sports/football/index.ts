// Football module exports
export { FootballModule } from './football.module';
export { FootballApiModule } from './football-api.module';
export { FootballWorkerModule } from './football-worker.module';
export { SyncModule } from './sync.module';
export { SeasonSyncModule } from './season-sync.module';

// Controllers
export { FixtureController } from './controllers/fixture.controller';
export { LeagueController } from './controllers/league.controller';
export { TeamController } from './controllers/team.controller';

// Services
export { FixtureService } from './services/fixture.service';
export { FixtureStatisticsService } from './services/fixture-statistics.service';
export { LeagueService } from './services/league.service';
export { TeamService } from './services/team.service';
export { TeamStatisticsService } from './services/team-statistics.service';
export { SyncService } from './services/sync.service';
export { SeasonSyncService } from './services/season-sync.service';

// Models/Entities
export { Fixture } from './models/fixture.entity';
export { FixtureStatistics } from './models/fixture-statistics.entity';
export { League } from './models/league.entity';
export { Team } from './models/team.entity';
export { TeamStatistics } from './models/team-statistics.entity';
export { Player } from './models/player.entity';
// Note: User entities moved to src/auth/entities/
// export { RegisteredUser } from './models/registered-user.entity';
// export { SystemUser } from './models/system-user.entity';

// DTOs
export * from './models/fixture.dto';
export * from './models/fixture-statistics.dto';
export * from './models/league.dto';
export * from './models/team.dto';
export * from './models/team-statistics.dto';
export * from './models/schedule.dto';

// Processors
export { SyncProcessor } from './sync.processor';
