import { Entity, Column, PrimaryColumn, Index } from 'typeorm';

@Entity('players')
export class Player {
    @PrimaryColumn()
    id: number;

    @Column()
    name: string;

    @Column({ nullable: true })
    firstName: string;

    @Column({ nullable: true })
    lastName: string;

    @Column()
    age: number;

    @Column({ nullable: true })
    nationality: string;

    @Column({ nullable: true })
    height: string;

    @Column({ nullable: true })
    weight: string;

    @Column()
    injured: boolean;

    @Column({ nullable: true })
    photo: string;

    @Column('jsonb', { nullable: true })
    statistics: {
        teamId: number;
        teamName: string;
        leagueId: number;
        gamesPlayed: number;
        goals: number;
        assists: number;
    };

    @Index()
    @Column({ type: 'bigint' })
    timestamp: number; // Unix timestamp của trận đấu liên quan

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}