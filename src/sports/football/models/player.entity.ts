import { Entity, Column, PrimaryGeneratedColumn, Index, OneToMany } from 'typeorm';
import { PlayerStatistics } from './player-statistics.entity';

@Entity('players')
export class Player {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({ unique: true })
    externalId: number;

    @Index()
    @Column()
    name: string;

    @Column({ nullable: true })
    firstName: string;

    @Column({ nullable: true })
    lastName: string;

    @Column({ nullable: true })
    age: number;

    @Column({ type: 'date', nullable: true })
    birthDate: Date;

    @Column({ nullable: true })
    birthPlace: string;

    @Column({ nullable: true })
    birthCountry: string;

    @Column({ nullable: true })
    nationality: string;

    @Column({ nullable: true })
    height: string;

    @Column({ nullable: true })
    weight: string;

    @Column({ type: 'boolean', default: false })
    injured: boolean;

    @Column({ nullable: true })
    photo: string;

    @Index()
    @Column({ type: 'bigint', default: () => 'EXTRACT(epoch FROM NOW()) * 1000' })
    timestamp: number;

    @OneToMany(() => PlayerStatistics, statistics => statistics.player)
    statistics: PlayerStatistics[];
}