import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('players')
export class Player {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true })
    @Index('idx_player_external_id')
    externalId: number;

    @Column()
    @Index('idx_player_name')
    name: string;

    @Column({ nullable: true })
    firstName: string;

    @Column({ nullable: true })
    lastName: string;

    @Column({ nullable: true })
    age: number;

    @Column({ type: 'date', nullable: true })
    birthDate: Date;

    @Column({ nullable: true })
    birthPlace: string;

    @Column({ nullable: true })
    birthCountry: string;

    @Column({ nullable: true })
    @Index('idx_player_nationality')
    nationality: string;

    @Column({ nullable: true })
    height: string;

    @Column({ nullable: true })
    weight: string;

    @Column({ type: 'boolean', default: false })
    injured: boolean;

    @Column({ nullable: true })
    photo: string;

    @Column({ nullable: true })
    @Index('idx_player_position')
    position: string;

    // Current team information for easy querying
    @Column({ nullable: true })
    @Index('idx_player_team_id')
    teamId: number;

    @Column({ nullable: true })
    teamName: string;

    @Column({ nullable: true })
    @Index('idx_player_league_id')
    leagueId: number;

    @Column({ nullable: true })
    leagueName: string;

    @Column({ nullable: true })
    @Index('idx_player_season')
    season: number;

    // Legacy statistics field for backward compatibility
    @Column('jsonb', { nullable: true })
    statistics: {
        teamId: number;
        teamName: string;
        leagueId: number;
        gamesPlayed: number;
        goals: number;
        assists: number;
    };

    // Relationship with detailed statistics - temporarily disabled
    // @OneToMany(() => PlayerStatistics, (stats: PlayerStatistics) => stats.player)
    // detailedStatistics: PlayerStatistics[];

    @Index()
    @Column({ type: 'bigint', nullable: true })
    timestamp: number; // Unix timestamp của trận đấu liên quan

    @CreateDateColumn({ type: 'timestamp with time zone' })
    createdAt: Date;

    @UpdateDateColumn({ type: 'timestamp with time zone' })
    updatedAt: Date;
}