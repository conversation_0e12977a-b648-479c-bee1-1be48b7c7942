import { IsInt, IsString, IsObject, IsOptional } from 'class-validator';

export class FixtureStatisticsDto {
    @IsInt()
    fixtureId: number;

    @IsString()
    teamName: string;

    @IsObject()
    statistics: {
        shotsOnGoal?: number;
        shotsOffGoal?: number;
        totalShots?: number;
        corners?: number;
        offsides?: number;
        yellowCards?: number;
        redCards?: number;
        possession?: string;
    };
}