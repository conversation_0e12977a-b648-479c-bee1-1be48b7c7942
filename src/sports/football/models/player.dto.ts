import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, Min, Max, IsNotEmpty } from 'class-validator';
import { Transform, Type } from 'class-transformer';

// Query DTOs
export class GetTopScorersDto {
    @ApiProperty({
        description: 'League ID',
        example: 39,
        type: Number
    })
    @IsNumber()
    @IsNotEmpty()
    @Type(() => Number)
    league: number;

    @ApiProperty({
        description: 'Season year',
        example: 2024,
        type: Number
    })
    @IsNumber()
    @IsNotEmpty()
    @Type(() => Number)
    season: number;

    @ApiPropertyOptional({
        description: 'Page number',
        example: 1,
        minimum: 1,
        default: 1
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Type(() => Number)
    page?: number = 1;

    @ApiPropertyOptional({
        description: 'Number of items per page',
        example: 20,
        minimum: 1,
        maximum: 50,
        default: 20
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(50)
    @Type(() => Number)
    limit?: number = 20;
}

export class GetTopAssistsDto {
    @ApiProperty({
        description: 'League ID',
        example: 39,
        type: Number
    })
    @IsNumber()
    @IsNotEmpty()
    @Type(() => Number)
    league: number;

    @ApiProperty({
        description: 'Season year',
        example: 2024,
        type: Number
    })
    @IsNumber()
    @IsNotEmpty()
    @Type(() => Number)
    season: number;

    @ApiPropertyOptional({
        description: 'Page number',
        example: 1,
        minimum: 1,
        default: 1
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Type(() => Number)
    page?: number = 1;

    @ApiPropertyOptional({
        description: 'Number of items per page',
        example: 20,
        minimum: 1,
        maximum: 50,
        default: 20
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(50)
    @Type(() => Number)
    limit?: number = 20;
}

export class GetPlayersDto {
    @ApiPropertyOptional({
        description: 'Team ID',
        example: 33,
        type: Number
    })
    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    team?: number;

    @ApiPropertyOptional({
        description: 'League ID',
        example: 39,
        type: Number
    })
    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    league?: number;

    @ApiPropertyOptional({
        description: 'Season year',
        example: 2024,
        type: Number
    })
    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    season?: number;

    @ApiPropertyOptional({
        description: 'Search by player name',
        example: 'haaland',
        type: String
    })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiPropertyOptional({
        description: 'Page number',
        example: 1,
        minimum: 1,
        default: 1
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Type(() => Number)
    page?: number = 1;

    @ApiPropertyOptional({
        description: 'Number of items per page',
        example: 20,
        minimum: 1,
        maximum: 50,
        default: 20
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(50)
    @Type(() => Number)
    limit?: number = 20;
}

// Response DTOs
export class PlayerBirthDto {
    @ApiProperty({ example: '2000-07-21', nullable: true })
    date: string | null;

    @ApiProperty({ example: 'Leeds', nullable: true })
    place: string | null;

    @ApiProperty({ example: 'England', nullable: true })
    country: string | null;
}

export class PlayerTeamDto {
    @ApiProperty({ example: 50 })
    id: number;

    @ApiProperty({ example: 'Manchester City' })
    name: string;

    @ApiProperty({ example: 'https://media.api-sports.io/football/teams/50.png' })
    logo: string;
}

export class PlayerLeagueDto {
    @ApiProperty({ example: 39 })
    id: number;

    @ApiProperty({ example: 'Premier League' })
    name: string;

    @ApiProperty({ example: 'England' })
    country: string;

    @ApiProperty({ example: 'https://media.api-sports.io/football/leagues/39.png' })
    logo: string;

    @ApiProperty({ example: 'https://media.api-sports.io/flags/gb.svg' })
    flag: string;

    @ApiProperty({ example: 2024 })
    season: number;
}

export class PlayerGamesDto {
    @ApiProperty({ example: 12 })
    appearences: number;

    @ApiProperty({ example: 11 })
    lineups: number;

    @ApiProperty({ example: 1020 })
    minutes: number;

    @ApiProperty({ example: null, nullable: true })
    number: number | null;

    @ApiProperty({ example: 'Attacker' })
    position: string;

    @ApiProperty({ example: '7.58', nullable: true })
    rating: string | null;

    @ApiProperty({ example: false })
    captain: boolean;
}

export class PlayerGoalsDto {
    @ApiProperty({ example: 12, nullable: true })
    total: number | null;

    @ApiProperty({ example: 0, nullable: true })
    conceded: number | null;

    @ApiProperty({ example: 0, nullable: true })
    assists: number | null;

    @ApiProperty({ example: null, nullable: true })
    saves: number | null;
}

export class PlayerStatisticsDto {
    @ApiProperty({ type: PlayerTeamDto })
    team: PlayerTeamDto;

    @ApiProperty({ type: PlayerLeagueDto })
    league: PlayerLeagueDto;

    @ApiProperty({ type: PlayerGamesDto })
    games: PlayerGamesDto;

    @ApiProperty({ type: PlayerGoalsDto })
    goals: PlayerGoalsDto;

    // Add more statistics as needed
}

export class PlayerDto {
    @ApiProperty({ example: 276 })
    id: number;

    @ApiProperty({ example: 'Erling Haaland' })
    name: string;

    @ApiProperty({ example: 'Erling' })
    firstname: string;

    @ApiProperty({ example: 'Haaland' })
    lastname: string;

    @ApiProperty({ example: 23 })
    age: number;

    @ApiProperty({ type: PlayerBirthDto })
    birth: PlayerBirthDto;

    @ApiProperty({ example: 'Norway' })
    nationality: string;

    @ApiProperty({ example: '194 cm' })
    height: string;

    @ApiProperty({ example: '88 kg' })
    weight: string;

    @ApiProperty({ example: false })
    injured: boolean;

    @ApiProperty({ example: 'https://media.api-sports.io/football/players/276.png' })
    photo: string;

    @ApiProperty({ type: [PlayerStatisticsDto] })
    statistics: PlayerStatisticsDto[];
}

export class PlayerResponseDto {
    @ApiProperty({ type: PlayerDto })
    player: PlayerDto;

    @ApiProperty({ type: [PlayerStatisticsDto] })
    statistics: PlayerStatisticsDto[];
}

export class PaginatedPlayersResponse {
    @ApiProperty({ type: [PlayerResponseDto] })
    data: PlayerResponseDto[];

    @ApiProperty({
        example: {
            totalItems: 100,
            totalPages: 5,
            currentPage: 1,
            limit: 20
        }
    })
    meta: {
        totalItems: number;
        totalPages: number;
        currentPage: number;
        limit: number;
    };

    @ApiProperty({ example: 200 })
    status: number;
}
