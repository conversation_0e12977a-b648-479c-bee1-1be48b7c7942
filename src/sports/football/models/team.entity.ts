import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

@Entity('teams')
export class Team {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true }) // Thêm UNIQUE constraint
  @Index('idx_team_externalId')
  externalId: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  code: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  season: number;

  @Column({ nullable: true })
  leagueId: number;

  @Column({ nullable: true })
  founded: number;

  @Column({ type: 'boolean', default: false })
  national: boolean;

  @Column('jsonb', { nullable: true })
  venue: {
    id?: number;
    name?: string;
    address?: string;
    city?: string;
    capacity?: number;
    surface?: string;
    image?: string;
  };

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}