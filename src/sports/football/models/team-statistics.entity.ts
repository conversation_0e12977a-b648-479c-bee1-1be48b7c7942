import { En<PERSON>ty, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('team_statistics')
export class TeamStatistics {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    @Index('idx_team_stats_teamId')
    teamId: number;

    @Column()
    @Index('idx_team_stats_leagueId')
    leagueId: number;

    @Column()
    @Index('idx_team_stats_season')
    season: number;

    @Column('jsonb')
    statistics: {
        played: {
            home: number;
            away: number;
            total: number;
        };
        wins: {
            home: number;
            away: number;
            total: number;
        };
        draws: {
            home: number;
            away: number;
            total: number;
        };
        loses: {
            home: number;
            away: number;
            total: number;
        };
        goals: {
            for: {
                home: number;
                away: number;
                total: number;
            };
            against: {
                home: number;
                away: number;
                total: number;
            };
        };
        biggest: {
            streak: {
                wins: number;
                draws: number;
                loses: number;
            };
            wins: {
                home: string;
                away: string;
            };
            loses: {
                home: string;
                away: string;
            };
            goals: {
                for: {
                    home: number;
                    away: number;
                };
                against: {
                    home: number;
                    away: number;
                };
            };
        };
        clean_sheet: {
            home: number;
            away: number;
            total: number;
        };
        failed_to_score: {
            home: number;
            away: number;
            total: number;
        };
        penalty: {
            scored: number;
            missed: number;
            total: number;
        };
        cards: {
            yellow: { minute: { [key: string]: number } };
            red: { minute: { [key: string]: number } };
        };
        lineups: {
            formation: string;
            played: number;
        }[];
    };

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}