import { IsInt, IsString, <PERSON>Optional, Min, Max, IsBoolean, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

/** DTO for querying teams */
export class GetTeamsDto {
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    league?: number;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    season?: number;

    @IsString()
    @IsOptional()
    country?: string;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @IsOptional()
    page: number = 1;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(100)
    @IsOptional()
    limit: number = 10;

    @IsString()
    @IsOptional()
    search?: string;
}

/** DTO for team response */
export class TeamResponseDto {
    @IsInt()
    id: number;

    @IsInt()
    externalId: number;

    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    code?: string;

    @IsString()
    @IsOptional()
    country?: string;

    @IsString()
    @IsOptional()
    logo?: string;

    @IsInt()
    @IsOptional()
    season?: number;

    @IsInt()
    @IsOptional()
    leagueId?: number;

    @IsInt()
    @IsOptional()
    founded?: number;

    @IsBoolean()
    national: boolean;

    @IsObject()
    @IsOptional()
    venue?: {
        id?: number;
        name?: string;
        address?: string;
        city?: string;
        capacity?: number;
        surface?: string;
        image?: string;
    };
}

/** Paginated response for teams */
export interface PaginatedTeamsResponse {
    data: TeamResponseDto[];
    meta: {
        totalItems: number;
        totalPages: number;
        currentPage: number;
        limit: number;
    };
    status: number;
}