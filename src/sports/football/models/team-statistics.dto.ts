import { IsInt, Min, IsObject, IsBoolean, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

/** DTO for querying team statistics */
export class GetTeamStatisticsDto {
    @Type(() => Number)
    @IsInt()
    @Min(1)
    league: number;

    @Type(() => Number)
    @IsInt()
    @Min(1)
    season: number;

    @Type(() => Number)
    @IsInt({ message: 'Team ID must be a valid integer' })
    @Min(1, { message: 'Team ID must be greater than 0' })
    team: number;

    @Type(() => Boolean)
    @IsBoolean()
    @IsOptional()
    newdb?: boolean;
}

/** DTO for team statistics response */
export class TeamStatisticsResponseDto {
    @IsInt()
    teamId: number;

    @IsInt()
    leagueId: number;

    @IsInt()
    season: number;

    @IsObject()
    statistics: {
        played: {
            home: number;
            away: number;
            total: number;
        };
        wins: {
            home: number;
            away: number;
            total: number;
        };
        draws: {
            home: number;
            away: number;
            total: number;
        };
        loses: {
            home: number;
            away: number;
            total: number;
        };
        goals: {
            for: {
                home: number;
                away: number;
                total: number;
            };
            against: {
                home: number;
                away: number;
                total: number;
            };
        };
        biggest: {
            streak: {
                wins: number;
                draws: number;
                loses: number;
            };
            wins: {
                home: string;
                away: string;
            };
            loses: {
                home: string;
                away: string;
            };
            goals: {
                for: {
                    home: number;
                    away: number;
                };
                against: {
                    home: number;
                    away: number;
                };
            };
        };
        clean_sheet: {
            home: number;
            away: number;
            total: number;
        };
        failed_to_score: {
            home: number;
            away: number;
            total: number;
        };
        penalty: {
            scored: number;
            missed: number;
            total: number;
        };
        cards: {
            yellow: { minute: { [key: string]: number } };
            red: { minute: { [key: string]: number } };
        };
        lineups: {
            formation: string;
            played: number;
        }[];
    };
}