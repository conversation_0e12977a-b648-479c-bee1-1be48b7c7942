import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { Player } from './player.entity';

@Entity('player_statistics')
@Unique(['playerId', 'teamId', 'leagueId', 'season'])
export class PlayerStatistics {
    @PrimaryGeneratedColumn()
    id: number;

    @ManyToOne(() => Player)
    @JoinColumn({ name: 'playerId' })
    player: Player;

    @Column()
    @Index('idx_player_stats_player')
    playerId: number;

    @Column()
    @Index('idx_player_stats_team')
    teamId: number;

    @Column({ nullable: true })
    teamName: string;

    @Column({ nullable: true })
    teamLogo: string;

    @Column()
    @Index('idx_player_stats_league')
    leagueId: number;

    @Column({ nullable: true })
    leagueName: string;

    @Column({ nullable: true })
    leagueCountry: string;

    @Column({ nullable: true })
    leagueLogo: string;

    @Column()
    @Index('idx_player_stats_season')
    season: number;

    // Games statistics
    @Column('jsonb', { nullable: true })
    games: {
        appearences: number;
        lineups: number;
        minutes: number;
        number: number | null;
        position: string;
        rating: string | null;
        captain: boolean;
    };

    // Substitutes statistics
    @Column('jsonb', { nullable: true })
    substitutes: {
        in: number;
        out: number;
        bench: number;
    };

    // Shots statistics
    @Column('jsonb', { nullable: true })
    shots: {
        total: number | null;
        on: number | null;
    };

    // Goals statistics
    @Column('jsonb', { nullable: true })
    goals: {
        total: number | null;
        conceded: number | null;
        assists: number | null;
        saves: number | null;
    };

    // Passes statistics
    @Column('jsonb', { nullable: true })
    passes: {
        total: number | null;
        key: number | null;
        accuracy: number | null;
    };

    // Tackles statistics
    @Column('jsonb', { nullable: true })
    tackles: {
        total: number | null;
        blocks: number | null;
        interceptions: number | null;
    };

    // Duels statistics
    @Column('jsonb', { nullable: true })
    duels: {
        total: number | null;
        won: number | null;
    };

    // Dribbles statistics
    @Column('jsonb', { nullable: true })
    dribbles: {
        attempts: number | null;
        success: number | null;
        past: number | null;
    };

    // Fouls statistics
    @Column('jsonb', { nullable: true })
    fouls: {
        drawn: number | null;
        committed: number | null;
    };

    // Cards statistics
    @Column('jsonb', { nullable: true })
    cards: {
        yellow: number;
        yellowred: number;
        red: number;
    };

    // Penalty statistics
    @Column('jsonb', { nullable: true })
    penalty: {
        won: number | null;
        commited: number | null;
        scored: number | null;
        missed: number | null;
        saved: number | null;
    };

    @CreateDateColumn({ type: 'timestamp with time zone' })
    createdAt: Date;

    @UpdateDateColumn({ type: 'timestamp with time zone' })
    updatedAt: Date;
}
