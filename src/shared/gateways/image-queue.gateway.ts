import {
    WebSocketGateway,
    WebSocketServer,
    OnGatewayConnection,
    OnGatewayDisconnect,
    SubscribeMessage,
    MessageBody,
    ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
// WebSocket doesn't need auth guards for now - can be added later if needed
import { ImageQueueService } from '../services/image-queue.service';
import { QueueStats, JobStatusResult } from '../types/image-queue.types';

@WebSocketGateway({
    cors: {
        origin: '*',
        methods: ['GET', 'POST'],
        credentials: true,
    },
    namespace: '/image-queue',
})
export class ImageQueueGateway implements OnGatewayConnection, OnGatewayDisconnect {
    @WebSocketServer()
    server: Server;

    private readonly logger = new Logger(ImageQueueGateway.name);
    private connectedClients = new Map<string, Socket>();

    constructor(private imageQueueService: ImageQueueService) { }

    afterInit(server: Server) {
        this.logger.log('WebSocket Gateway initialized for image queue');
    }

    handleConnection(client: Socket) {
        this.connectedClients.set(client.id, client);
        this.logger.log(`Client connected: ${client.id}`);

        // Send current queue stats to new client
        this.sendQueueStats(client);
    }

    handleDisconnect(client: Socket) {
        this.connectedClients.delete(client.id);
        this.logger.log(`Client disconnected: ${client.id}`);
    }

    /**
     * Subscribe to job status updates
     */
    @SubscribeMessage('subscribe_job')
    async handleSubscribeJob(
        @MessageBody() data: { jobId: string },
        @ConnectedSocket() client: Socket,
    ) {
        const { jobId } = data;

        // Join room for this specific job
        client.join(`job_${jobId}`);

        // Send current job status
        const status = await this.imageQueueService.getJobStatus(jobId);
        client.emit('job_status', { jobId, ...status });

        this.logger.debug(`Client ${client.id} subscribed to job ${jobId}`);
    }

    /**
     * Unsubscribe from job status updates
     */
    @SubscribeMessage('unsubscribe_job')
    handleUnsubscribeJob(
        @MessageBody() data: { jobId: string },
        @ConnectedSocket() client: Socket,
    ) {
        const { jobId } = data;
        client.leave(`job_${jobId}`);
        this.logger.debug(`Client ${client.id} unsubscribed from job ${jobId}`);
    }

    /**
     * Subscribe to queue statistics
     */
    @SubscribeMessage('subscribe_queue_stats')
    async handleSubscribeQueueStats(@ConnectedSocket() client: Socket) {
        client.join('queue_stats');
        await this.sendQueueStats(client);
        this.logger.debug(`Client ${client.id} subscribed to queue stats`);
    }

    /**
     * Get current job status
     */
    @SubscribeMessage('get_job_status')
    async handleGetJobStatus(
        @MessageBody() data: { jobId: string },
        @ConnectedSocket() client: Socket,
    ) {
        const { jobId } = data;
        const status = await this.imageQueueService.getJobStatus(jobId);
        client.emit('job_status', { jobId, ...status });
    }

    /**
     * Get current queue statistics
     */
    @SubscribeMessage('get_queue_stats')
    async handleGetQueueStats(@ConnectedSocket() client: Socket) {
        await this.sendQueueStats(client);
    }

    /**
     * Notify all clients about job completion
     */
    notifyJobCompleted(jobId: string, filePath: string, imageType: string) {
        this.server.to(`job_${jobId}`).emit('job_completed', {
            jobId,
            filePath,
            imageType,
            status: 'completed',
            timestamp: new Date().toISOString(),
        });

        this.logger.debug(`Notified job completion: ${jobId} -> ${filePath}`);
    }

    /**
     * Notify all clients about job failure
     */
    notifyJobFailed(jobId: string, error: string, imageType: string) {
        this.server.to(`job_${jobId}`).emit('job_failed', {
            jobId,
            error,
            imageType,
            status: 'failed',
            timestamp: new Date().toISOString(),
        });

        this.logger.debug(`Notified job failure: ${jobId} -> ${error}`);
    }

    /**
     * Notify all clients about job started
     */
    notifyJobStarted(jobId: string, imageType: string, fileName: string) {
        this.server.to(`job_${jobId}`).emit('job_started', {
            jobId,
            imageType,
            fileName,
            status: 'processing',
            timestamp: new Date().toISOString(),
        });

        this.logger.debug(`Notified job started: ${jobId} -> ${fileName}`);
    }

    /**
     * Broadcast queue statistics to all subscribed clients
     */
    async broadcastQueueStats() {
        const stats = await this.imageQueueService.getQueueStats();
        this.server.to('queue_stats').emit('queue_stats', {
            ...stats,
            timestamp: new Date().toISOString(),
        });
    }

    /**
     * Send queue stats to specific client
     */
    private async sendQueueStats(client: Socket) {
        try {
            const stats = await this.imageQueueService.getQueueStats();
            client.emit('queue_stats', {
                ...stats,
                timestamp: new Date().toISOString(),
            });
        } catch (error) {
            this.logger.error(`Failed to send queue stats: ${error.message}`);
        }
    }

    /**
     * Broadcast image download progress (for future use)
     */
    notifyDownloadProgress(jobId: string, progress: number, imageType: string) {
        this.server.to(`job_${jobId}`).emit('download_progress', {
            jobId,
            progress,
            imageType,
            timestamp: new Date().toISOString(),
        });
    }

    /**
     * Get connected clients count
     */
    getConnectedClientsCount(): number {
        return this.connectedClients.size;
    }

    /**
     * Broadcast system message to all clients
     */
    broadcastSystemMessage(message: string, type: 'info' | 'warning' | 'error' = 'info') {
        this.server.emit('system_message', {
            message,
            type,
            timestamp: new Date().toISOString(),
        });
    }
}
