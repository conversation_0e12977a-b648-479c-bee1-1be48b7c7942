export type QueuePriority = 'high' | 'normal' | 'low';
export type JobStatus = 'queued' | 'processing' | 'completed' | 'failed' | 'not_found';
export type ImageType = 'teams' | 'leagues' | 'venues' | 'flags' | 'players';

export interface QueueItem {
    id: string;                    // Unique job ID
    url: string;                   // Image URL to download
    type: ImageType;               // Type of image
    fileName: string;              // Target filename
    priority: QueuePriority;       // Queue priority
    retries: number;               // Current retry count
    maxRetries: number;            // Maximum retry attempts
    createdAt: number;             // Timestamp when created
    startedAt?: number;            // Timestamp when processing started
    completedAt?: number;          // Timestamp when completed
    error?: string;                // Error message if failed
    filePath?: string;             // Final file path when completed
}

export interface JobStatusResult {
    status: JobStatus;
    data?: QueueItem;
    message?: string;
}

export interface DownloadResult {
    path: string;                  // File path (real or placeholder)
    status: 'ready' | 'downloading' | 'queued';
    jobId?: string;                // Job ID for tracking
    isPlaceholder?: boolean;       // Whether path is placeholder
}

export interface QueueStats {
    queued: {
        high: number;
        normal: number;
        low: number;
        total: number;
    };
    processing: number;
    completed: number;
    failed: number;
    totalProcessed: number;
}

export interface PerformanceMetrics {
    averageProcessingTime: number; // Average time in milliseconds
    currentlyProcessing: number;   // Number of jobs currently processing
    completedToday: number;        // Jobs completed in last 24 hours
    failedToday: number;           // Jobs failed in last 24 hours
    successRate: number;           // Success rate percentage
    queueThroughput: number;       // Jobs per hour
}

export const QUEUE_KEYS = {
    HIGH_PRIORITY: 'image_queue:high',
    NORMAL_PRIORITY: 'image_queue:normal',
    LOW_PRIORITY: 'image_queue:low',
    PROCESSING: 'image_queue:processing',
    COMPLETED: 'image_queue:completed',
    FAILED: 'image_queue:failed',
    STATS: 'image_queue:stats',
    DUPLICATE_CHECK: 'image_queue:duplicates'
} as const;

export const PRIORITY_ORDER: QueuePriority[] = ['high', 'normal', 'low'];

export const DEFAULT_PRIORITIES: Record<ImageType, QueuePriority> = {
    teams: 'high',      // Team logos are user-facing, high priority
    leagues: 'high',    // League logos are user-facing, high priority
    players: 'high',    // Player photos are user-facing, high priority
    venues: 'normal',   // Venue images are less critical
    flags: 'low'        // Country flags are background data
};