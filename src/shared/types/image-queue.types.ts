import { Injectable, Inject, Logger } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import configuration from '../../core/config/configuration';
import { ImageQueueService } from './image-queue.service';
import { DownloadResult, ImageType, QueueItem } from '../types/image-queue.types';

@Injectable()
export class ImageService {
    private readonly logger = new Logger(ImageService.name);
    private isProcessingQueue = false;
    private lastRequestTime = 0;

    constructor(
        @Inject(configuration.KEY)
        private config: ConfigType<typeof configuration>,
        private imageQueueService: ImageQueueService,
    ) { }

    /**
     * Download an image from a URL and save it locally if it doesn't exist
     * For backward compatibility, returns string path (placeholder if not ready)
     * @param url - The URL of the image to download
     * @param type - The type of image (e.g., 'leagues', 'teams', 'flags', 'venues')
     * @param fileName - The name of the file to save
     * @returns File path (real or placeholder)
     */
    async downloadImage(url: string, type: string, fileName: string): Promise<string> {
        const targetPath = path.join(this.config.imageStoragePath, type, fileName);

        // Check if file already exists
        if (fs.existsSync(targetPath)) {
            return targetPath;
        }

        // Add to Redis queue for background processing
        await this.imageQueueService.addToQueue(url, type as ImageType, fileName);

        // Start queue processing if not already running
        this.processQueue();

        // Return placeholder immediately for backward compatibility
        return this.getPlaceholderPath(type);
    }

    /**
     * Legacy method for backward compatibility - blocks until download complete
     * @deprecated Use downloadImageAsync instead for better performance
     */
    async downloadImageBlocking(url: string, type: string, fileName: string): Promise<string> {
        const result = await this.downloadImageAsync(url, type, fileName);

        if (result.status === 'ready') {
            return result.path;
        }

        // Wait for job completion if queued
        if (result.jobId) {
            return this.waitForJobCompletion(result.jobId, path.join(this.config.imageStoragePath, type, fileName));
        }

        throw new Error('Failed to download image');
    }

    /**
     * New async method with Redis queue - returns DownloadResult
     */
    async downloadImageAsync(url: string, type: string, fileName: string): Promise<DownloadResult> {
        const targetPath = path.join(this.config.imageStoragePath, type, fileName);

        // Check if file already exists
        if (fs.existsSync(targetPath)) {
            return {
                path: targetPath,
                status: 'ready'
            };
        }

        // Add to Redis queue for background processing
        const jobId = await this.imageQueueService.addToQueue(url, type as ImageType, fileName);

        // Start queue processing if not already running
        this.processQueue();

        // Return placeholder immediately
        return {
            path: this.getPlaceholderPath(type),
            status: 'queued',
            jobId,
            isPlaceholder: true
        };
    }

    /**
     * Internal method to download image with rate limiting
     */
    private async downloadImageInternal(url: string, type: string, fileName: string): Promise<string> {
        const folderPath = path.join(this.config.imageStoragePath, type);
        const filePath = path.join(folderPath, fileName);

        // Kiểm tra file đã tồn tại
        if (fs.existsSync(filePath)) {
            return filePath;
        }

        // Tạo thư mục nếu chưa tồn tại
        fs.mkdirSync(folderPath, { recursive: true });

        // Rate limiting: Ensure minimum interval between requests
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = this.config.imageDownload.minRequestInterval;
        if (timeSinceLastRequest < minInterval) {
            const waitTime = minInterval - timeSinceLastRequest;
            this.logger.debug(`Rate limiting: waiting ${waitTime}ms before downloading ${url}`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        this.lastRequestTime = Date.now();

        // Enhanced retry logic with exponential backoff for rate limiting
        const maxRetries = this.config.imageDownload.maxRetries;
        let retryDelay = this.config.imageDownload.baseRetryDelay;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const writer = fs.createWriteStream(filePath);
                const response = await axios({
                    url,
                    method: 'GET',
                    responseType: 'stream',
                    timeout: this.config.imageDownload.timeout,
                    headers: {
                        'User-Agent': 'APISportsGame/1.0',
                        'Accept': 'image/*',
                    },
                });
                response.data.pipe(writer);
                return new Promise((resolve, reject) => {
                    writer.on('finish', () => resolve(filePath));
                    writer.on('error', reject);
                });
            } catch (error) {
                const status = error.response?.status;

                if (attempt === maxRetries) {
                    this.logger.error(`Failed to download image ${url} after ${maxRetries} attempts: ${error.message}`);
                    throw new Error(`Failed to download image ${url} after ${maxRetries} attempts: ${error.message}`);
                }

                // Calculate delay based on error type
                let currentDelay = retryDelay;

                if (status === 429) {
                    // Rate limited - use exponential backoff with longer delays
                    currentDelay = Math.min(retryDelay * Math.pow(2, attempt - 1), 60000); // Max 60 seconds
                    this.logger.warn(
                        `Rate limited (HTTP 429) for ${url}. Attempt ${attempt}/${maxRetries}. Waiting ${currentDelay}ms before retry...`,
                    );
                } else if (status >= 500) {
                    // Server error - moderate delay
                    currentDelay = retryDelay * attempt;
                    this.logger.warn(
                        `Server error (HTTP ${status}) for ${url}. Attempt ${attempt}/${maxRetries}. Retrying after ${currentDelay}ms...`,
                    );
                } else {
                    // Other errors - standard delay
                    this.logger.warn(
                        `Attempt ${attempt}/${maxRetries} failed for ${url}: ${error.message}${status ? ` (HTTP ${status})` : ''}. Retrying after ${currentDelay}ms...`,
                    );
                }

                await new Promise((resolve) => setTimeout(resolve, currentDelay));
            }
        }

        throw new Error('Unexpected error in retry logic');
    }

    /**
     * Process the Redis queue with rate limiting
     */
    private async processQueue(): Promise<void> {
        if (this.isProcessingQueue) {
            return;
        }

        this.isProcessingQueue = true;

        try {
            while (true) {
                const queueItem = await this.imageQueueService.getNextJob();
                if (!queueItem) {
                    break; // No more jobs in queue
                }

                try {
                    const filePath = await this.downloadImageInternal(queueItem.url, queueItem.type, queueItem.fileName);
                    await this.imageQueueService.markJobCompleted(queueItem.id, filePath);
                    this.logger.debug(`Successfully downloaded: ${queueItem.id} -> ${filePath}`);
                } catch (error) {
                    await this.imageQueueService.markJobFailed(queueItem.id, error.message);
                    this.logger.error(`Failed to download: ${queueItem.id} -> ${error.message}`);
                }

                // Rate limiting between downloads
                await this.waitForRateLimit();
            }
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * Get placeholder path for image type
     */
    private getPlaceholderPath(type: string): string {
        const placeholders: Record<string, string> = {
            'teams': 'team-placeholder.png',
            'leagues': 'league-placeholder.png',
            'venues': 'venue-placeholder.png',
            'flags': 'flag-placeholder.png'
        };

        const placeholder = placeholders[type] || 'image-placeholder.png';
        return path.join(this.config.imageStoragePath, 'placeholders', placeholder);
    }

    /**
     * Wait for job completion (for backward compatibility)
     */
    private async waitForJobCompletion(jobId: string, expectedPath: string): Promise<string> {
        const maxWaitTime = 60000; // 60 seconds max wait
        const checkInterval = 1000; // Check every 1 second
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
            const status = await this.imageQueueService.getJobStatus(jobId);

            if (status.status === 'completed' && status.data?.filePath) {
                return status.data.filePath;
            }

            if (status.status === 'failed') {
                throw new Error(`Job ${jobId} failed: ${status.data?.error || 'Unknown error'}`);
            }

            // Check if file exists (in case job completed but status not updated)
            if (fs.existsSync(expectedPath)) {
                return expectedPath;
            }

            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }

        throw new Error(`Timeout waiting for job ${jobId} to complete`);
    }

    /**
     * Wait for rate limiting
     */
    private async waitForRateLimit(): Promise<void> {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minInterval = this.config.imageDownload.minRequestInterval;

        if (timeSinceLastRequest < minInterval) {
            const waitTime = minInterval - timeSinceLastRequest;
            this.logger.debug(`Rate limiting: waiting ${waitTime}ms`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        this.lastRequestTime = Date.now();
    }
}