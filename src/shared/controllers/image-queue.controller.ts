import { Controller, Get, Post, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { SystemJwtAuthGuard } from '../../auth/system/guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../../auth/system/guards/system-roles.guard';
import { Roles } from '../../auth/core/decorators/auth.decorators';
import { SystemRole } from '../../auth/core/types/auth.types';
import { ImageQueueService } from '../services/image-queue.service';
import { ImageQueueGateway } from '../gateways/image-queue.gateway';
import { QueueStats, JobStatusResult } from '../types/image-queue.types';

@ApiTags('Image Queue Management')
@Controller('queue')
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
@ApiBearerAuth()
export class ImageQueueController {
    constructor(
        private imageQueueService: ImageQueueService,
        private imageQueueGateway: ImageQueueGateway,
    ) { }

    @Get('stats')
    @Roles(SystemRole.ADMIN, SystemRole.MODERATOR)
    @ApiOperation({
        summary: 'Get queue statistics',
        description: 'Get current queue statistics including pending, processing, completed, and failed jobs'
    })
    @ApiResponse({
        status: 200,
        description: 'Queue statistics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        queued: {
                            type: 'object',
                            properties: {
                                high: { type: 'number', example: 5 },
                                normal: { type: 'number', example: 12 },
                                low: { type: 'number', example: 3 },
                                total: { type: 'number', example: 20 }
                            }
                        },
                        processing: { type: 'number', example: 2 },
                        completed: { type: 'number', example: 150 },
                        failed: { type: 'number', example: 5 },
                        totalProcessed: { type: 'number', example: 155 }
                    }
                },
                status: { type: 'number', example: 200 }
            }
        }
    })
    async getQueueStats(): Promise<{ data: QueueStats; status: number }> {
        const stats = await this.imageQueueService.getQueueStats();
        return { data: stats, status: 200 };
    }

    @Get('job/:jobId')
    @Roles(SystemRole.ADMIN, SystemRole.MODERATOR, SystemRole.EDITOR)
    @ApiOperation({
        summary: 'Get job status',
        description: 'Get the current status of a specific image download job'
    })
    @ApiParam({
        name: 'jobId',
        description: 'Unique job identifier',
        example: 'img_1640995200000_abc123def'
    })
    @ApiResponse({
        status: 200,
        description: 'Job status retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        status: {
                            type: 'string',
                            enum: ['queued', 'processing', 'completed', 'failed', 'not_found'],
                            example: 'completed'
                        },
                        data: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', example: 'img_1640995200000_abc123def' },
                                url: { type: 'string', example: 'https://api-sports.io/team/33.png' },
                                type: { type: 'string', example: 'teams' },
                                fileName: { type: 'string', example: '33.png' },
                                priority: { type: 'string', example: 'high' },
                                retries: { type: 'number', example: 0 },
                                createdAt: { type: 'number', example: 1640995200000 },
                                completedAt: { type: 'number', example: 1640995205000 },
                                filePath: { type: 'string', example: 'public/images/teams/33.png' }
                            }
                        }
                    }
                },
                status: { type: 'number', example: 200 }
            }
        }
    })
    @ApiResponse({ status: 404, description: 'Job not found' })
    async getJobStatus(@Param('jobId') jobId: string): Promise<{ data: JobStatusResult; status: number }> {
        const result = await this.imageQueueService.getJobStatus(jobId);
        return { data: result, status: result.status === 'not_found' ? 404 : 200 };
    }

    @Post('cleanup')
    @Roles(SystemRole.ADMIN)
    @ApiOperation({
        summary: 'Clean up old jobs',
        description: 'Remove old completed and failed jobs from Redis to free up memory'
    })
    @ApiQuery({
        name: 'hours',
        required: false,
        description: 'Remove jobs older than specified hours (default: 24)',
        example: 24
    })
    @ApiResponse({
        status: 200,
        description: 'Cleanup completed successfully',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        cleaned: { type: 'number', example: 45 },
                        message: { type: 'string', example: 'Cleaned up 45 old jobs' }
                    }
                },
                status: { type: 'number', example: 200 }
            }
        }
    })
    async cleanupOldJobs(@Query('hours') hours?: number): Promise<{ data: any; status: number }> {
        const result = await this.imageQueueService.cleanupOldJobs();
        return {
            data: {
                ...result,
                message: `Cleaned up ${result.cleaned} old jobs`
            },
            status: 200
        };
    }

    @Get('health')
    @Roles(SystemRole.ADMIN, SystemRole.MODERATOR)
    @ApiOperation({
        summary: 'Get queue health status',
        description: 'Get overall health status of the image queue system'
    })
    @ApiResponse({
        status: 200,
        description: 'Queue health status',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        healthy: { type: 'boolean', example: true },
                        connectedClients: { type: 'number', example: 5 },
                        redisConnected: { type: 'boolean', example: true },
                        queueStats: {
                            type: 'object',
                            properties: {
                                totalQueued: { type: 'number', example: 20 },
                                processing: { type: 'number', example: 2 },
                                completed: { type: 'number', example: 150 },
                                failed: { type: 'number', example: 5 }
                            }
                        },
                        lastUpdate: { type: 'string', example: '2024-01-01T12:00:00.000Z' }
                    }
                },
                status: { type: 'number', example: 200 }
            }
        }
    })
    async getQueueHealth(): Promise<{ data: any; status: number }> {
        const stats = await this.imageQueueService.getQueueStats();
        const connectedClients = this.imageQueueGateway.getConnectedClientsCount();

        const health = {
            healthy: true,
            connectedClients,
            redisConnected: true, // Could add actual Redis ping check
            queueStats: {
                totalQueued: stats.queued.total,
                processing: stats.processing,
                completed: stats.completed,
                failed: stats.failed
            },
            lastUpdate: new Date().toISOString()
        };

        return { data: health, status: 200 };
    }

    @Post('broadcast/:message')
    @Roles(SystemRole.ADMIN)
    @ApiOperation({
        summary: 'Broadcast system message',
        description: 'Send a system message to all connected WebSocket clients'
    })
    @ApiParam({
        name: 'message',
        description: 'Message to broadcast',
        example: 'System maintenance in 5 minutes'
    })
    @ApiQuery({
        name: 'type',
        required: false,
        description: 'Message type',
        enum: ['info', 'warning', 'error'],
        example: 'warning'
    })
    @ApiResponse({
        status: 200,
        description: 'Message broadcasted successfully',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        message: { type: 'string', example: 'System maintenance in 5 minutes' },
                        type: { type: 'string', example: 'warning' },
                        sentTo: { type: 'number', example: 5 },
                        timestamp: { type: 'string', example: '2024-01-01T12:00:00.000Z' }
                    }
                },
                status: { type: 'number', example: 200 }
            }
        }
    })
    async broadcastMessage(
        @Param('message') message: string,
        @Query('type') type: 'info' | 'warning' | 'error' = 'info'
    ): Promise<{ data: any; status: number }> {
        const connectedClients = this.imageQueueGateway.getConnectedClientsCount();
        this.imageQueueGateway.broadcastSystemMessage(message, type);

        return {
            data: {
                message,
                type,
                sentTo: connectedClients,
                timestamp: new Date().toISOString()
            },
            status: 200
        };
    }
}
