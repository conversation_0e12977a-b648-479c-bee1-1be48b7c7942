import { Injectable } from '@nestjs/common';

@Injectable()
export class UtilsService {
    generateSlug(input: string, date?: string): string {
        const slug = input
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
        return date ? `${slug}-${date}` : slug;
    }

    formatDate(date: Date): string {
        return date.toISOString().split('T')[0];
    }

    /**
     * Get current UTC time - always returns UTC regardless of system timezone
     * @returns Date object in UTC
     */
    getUtcNow(): Date {
        return new Date(new Date().toISOString());
    }

    /**
     * Parse date string to UTC Date object
     * @param dateString - Date string from API
     * @returns Date object in UTC
     */
    parseUtcDate(dateString: string): Date {
        return new Date(new Date(dateString).toISOString());
    }

    /**
     * Get UTC timestamp in milliseconds
     * @returns UTC timestamp
     */
    getUtcTimestamp(): number {
        return new Date().getTime(); // getTime() always returns UTC
    }

    /**
     * Calculate time difference in minutes (UTC-safe)
     * @param futureDate - Future date
     * @param currentDate - Current date (optional, defaults to UTC now)
     * @returns Difference in minutes
     */
    getMinutesDifference(futureDate: Date, currentDate?: Date): number {
        const current = currentDate || this.getUtcNow();
        const future = new Date(futureDate.toISOString());
        return Math.round((future.getTime() - current.getTime()) / (1000 * 60));
    }
}