import { Module, Global, OnModuleInit } from '@nestjs/common';
import { ImageService } from './services/image.service';
import { ImageQueueService } from './services/image-queue.service';
import { UtilsService } from './services/utils.service';
import { ImageQueueGateway } from './gateways/image-queue.gateway';
import { ImageQueueController } from './controllers/image-queue.controller';

@Global()
@Module({
    providers: [ImageService, ImageQueueService, UtilsService, ImageQueueGateway],
    controllers: [ImageQueueController],
    exports: [ImageService, ImageQueueService, UtilsService, ImageQueueGateway],
})
export class SharedModule implements OnModuleInit {
    constructor(
        private imageQueueService: ImageQueueService,
        private imageQueueGateway: ImageQueueGateway,
    ) { }

    onModuleInit() {
        // Connect gateway to service to avoid circular dependency
        this.imageQueueService.setGateway(this.imageQueueGateway);
    }
}