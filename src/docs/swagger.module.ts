import { DynamicModule, Module } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule as NestSwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

/**
 * Module for configuring Swagger documentation
 */
@Module({})
export class SwaggerModule {
    static forRoot(): DynamicModule {
        return {
            module: SwaggerModule,
            providers: [],
            exports: [],
        };
    }

    /**
     * Configures Swagger for the application
     * @param app - NestJS application instance
     */
    static configure(app: INestApplication): void {
        const config = new DocumentBuilder()
            .setTitle('APISportsGame API')
            .setDescription('API proxy for sports data, starting with football')
            .setVersion('1.0.0')
            .addApiKey(
                { type: 'apiKey', name: 'x-apisports-key', in: 'header' },
                'apiKey',
            )
            .build();
        const document = NestSwaggerModule.createDocument(app, config);
        NestSwaggerModule.setup('api', app, document);
    }
}