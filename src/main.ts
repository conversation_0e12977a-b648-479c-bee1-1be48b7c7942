import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { setupSwagger } from './docs/swagger.config';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // Get configuration
  const configService = app.get(ConfigService);
  const config = configService.get('app');

  // Serve static files for uploaded images
  const imageStoragePath = configService.get<string>('IMAGE_STORAGE_PATH', './public/images');
  app.useStaticAssets(join(process.cwd(), imageStoragePath), {
    prefix: '/uploads/',
  });

  // Setup Swagger documentation
  setupSwagger(app, config);

  console.log('APISportsGame started');
  console.log('📚 API Documentation available at: http://localhost:3000/api-docs');
  console.log('🖼️  Uploaded images available at: http://localhost:3000/uploads/');

  await app.listen(3000);
}
bootstrap();