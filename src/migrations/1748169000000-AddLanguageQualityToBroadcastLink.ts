import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddLanguageQualityToBroadcastLink1748169000000 implements MigrationInterface {
    name = 'AddLanguageQualityToBroadcastLink1748169000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add language column
        await queryRunner.addColumn('broadcast_links', new TableColumn({
            name: 'language',
            type: 'varchar',
            isNullable: true,
            comment: 'Language of the broadcast (e.g., English, Spanish, French)'
        }));

        // Add quality column
        await queryRunner.addColumn('broadcast_links', new TableColumn({
            name: 'quality',
            type: 'varchar',
            isNullable: true,
            comment: 'Quality of the broadcast (e.g., HD, 4K, SD)'
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove quality column
        await queryRunner.dropColumn('broadcast_links', 'quality');
        
        // Remove language column
        await queryRunner.dropColumn('broadcast_links', 'language');
    }
}
