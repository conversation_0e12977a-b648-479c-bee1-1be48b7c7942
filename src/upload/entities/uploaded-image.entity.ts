import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ImageCategory } from '../upload.dto';

@Entity('uploaded_images')
@Index(['category'])
@Index(['uploadedBy'])
@Index(['uploadedAt'])
export class UploadedImage {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true })
    imageId: string; // Unique identifier like 'img_1234567890'

    @Column()
    originalName: string;

    @Column()
    filename: string; // Stored filename with path

    @Column()
    size: number; // File size in bytes

    @Column()
    mimeType: string;

    @Column({
        type: 'enum',
        enum: ImageCategory,
        default: ImageCategory.GENERAL
    })
    category: ImageCategory;

    @Column()
    path: string; // Local file path

    @Column()
    url: string; // Public URL

    @Column({ nullable: true })
    description?: string;

    @Column()
    uploadedBy: number; // SystemUser ID

    @Column({ nullable: true })
    sourceUrl?: string; // Original URL if uploaded from URL

    @CreateDateColumn()
    uploadedAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
