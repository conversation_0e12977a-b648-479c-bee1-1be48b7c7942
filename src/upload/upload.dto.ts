import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUrl, IsOptional, IsEnum, MaxLength } from 'class-validator';

export enum ImageCategory {
    LEAGUES = 'leagues',
    TEAMS = 'teams',
    FLAGS = 'flags',
    VENUES = 'venues',
    GENERAL = 'general'
}

export class UploadImageByUrlDto {
    @ApiProperty({
        description: 'URL of the image to upload',
        example: 'https://media.api-sports.io/football/leagues/39.png'
    })
    @IsUrl({}, { message: 'Invalid URL format' })
    @IsString()
    imageUrl: string;

    @ApiProperty({
        description: 'Category for organizing images',
        enum: ImageCategory,
        example: ImageCategory.LEAGUES
    })
    @IsEnum(ImageCategory)
    category: ImageCategory;

    @ApiProperty({
        description: 'Custom filename (optional, will auto-generate if not provided)',
        example: 'premier-league-logo',
        required: false
    })
    @IsOptional()
    @IsString()
    @MaxLength(100)
    filename?: string;

    @ApiProperty({
        description: 'Description or alt text for the image',
        example: 'Premier League official logo',
        required: false
    })
    @IsOptional()
    @IsString()
    @MaxLength(255)
    description?: string;
}

export class UploadImageResponseDto {
    @ApiProperty({
        description: 'Unique identifier for the uploaded image',
        example: 'img_1234567890'
    })
    id: string;

    @ApiProperty({
        description: 'Original filename',
        example: 'premier-league-logo.png'
    })
    originalName: string;

    @ApiProperty({
        description: 'Stored filename',
        example: 'leagues/premier-league-logo-1234567890.png'
    })
    filename: string;

    @ApiProperty({
        description: 'File size in bytes',
        example: 15420
    })
    size: number;

    @ApiProperty({
        description: 'MIME type',
        example: 'image/png'
    })
    mimeType: string;

    @ApiProperty({
        description: 'Image category',
        enum: ImageCategory,
        example: ImageCategory.LEAGUES
    })
    category: ImageCategory;

    @ApiProperty({
        description: 'Public URL to access the image',
        example: 'http://localhost:3000/uploads/leagues/premier-league-logo-1234567890.png'
    })
    url: string;

    @ApiProperty({
        description: 'Local file path',
        example: './public/images/leagues/premier-league-logo-1234567890.png'
    })
    path: string;

    @ApiProperty({
        description: 'Upload timestamp',
        example: '2025-05-25T10:30:00.000Z'
    })
    uploadedAt: Date;

    @ApiProperty({
        description: 'User who uploaded the image',
        example: 1
    })
    uploadedBy: number;

    @ApiProperty({
        description: 'Image description',
        example: 'Premier League official logo',
        required: false
    })
    description?: string;
}

export class ImageListResponseDto {
    @ApiProperty({
        description: 'Array of uploaded images',
        type: [UploadImageResponseDto]
    })
    data: UploadImageResponseDto[];

    @ApiProperty({
        description: 'Pagination metadata',
        example: {
            totalItems: 150,
            totalPages: 8,
            currentPage: 1,
            limit: 20
        }
    })
    meta: {
        totalItems: number;
        totalPages: number;
        currentPage: number;
        limit: number;
    };
}

export class GetImagesDto {
    @ApiProperty({
        description: 'Page number',
        example: 1,
        required: false
    })
    @IsOptional()
    page?: number = 1;

    @ApiProperty({
        description: 'Items per page',
        example: 20,
        required: false
    })
    @IsOptional()
    limit?: number = 20;

    @ApiProperty({
        description: 'Filter by category',
        enum: ImageCategory,
        required: false
    })
    @IsOptional()
    @IsEnum(ImageCategory)
    category?: ImageCategory;

    @ApiProperty({
        description: 'Search by filename or description',
        example: 'premier',
        required: false
    })
    @IsOptional()
    @IsString()
    @MaxLength(100)
    search?: string;
}
