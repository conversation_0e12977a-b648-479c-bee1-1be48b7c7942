import {
    Controller,
    Post,
    Get,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    UseInterceptors,
    UploadedFile,
    ParseEnumPipe,
    BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiParam,
    ApiQuery,
    ApiBody,
    ApiConsumes,
    ApiBearerAuth,
} from '@nestjs/swagger';
import { SystemJwtAuthGuard } from '../auth/system/guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../auth/system/guards/system-roles.guard';
import { GetCurrentUser } from '../auth/core/decorators/auth.decorators';
import { SystemUser } from '../auth/system/entities/system-user.entity';
import { UploadService } from './upload.service';
import {
    UploadImageByUrlDto,
    UploadImageResponseDto,
    ImageListResponseDto,
    GetImagesDto,
    ImageCategory,
} from './upload.dto';

@ApiTags('Image Upload')
@ApiBearerAuth('bearer')
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
@Controller('upload')
export class UploadController {
    constructor(private readonly uploadService: UploadService) { }

    @ApiOperation({
        summary: 'Upload Image from File (SystemUser Only)',
        description: `
        Upload an image file to the server with automatic categorization and storage.

        **🔒 AUTHENTICATION REQUIRED:**
        SystemUser authentication required (Admin/Moderator/Editor roles accepted).

        **Quick Auth Setup in Swagger UI:**
        1. Login: POST /system-auth/login
        2. Copy "accessToken" from response
        3. Click "Authorize" button (🔓) at top of page
        4. Enter: Bearer YOUR_ACCESS_TOKEN
        5. Click "Authorize" and "Close"

        **Test Credentials:**
        - Admin: {"username": "admin", "password": "admin123456"}
        - Editor: {"username": "editor1", "password": "editor123456"}
        - Moderator: {"username": "moderator1", "password": "moderator123456"}

        **Features:**
        - File validation (PNG, JPG, JPEG, GIF, SVG)
        - Size limit: 10MB maximum
        - Automatic filename generation
        - Category-based organization
        - Duplicate prevention
        - Audit logging

        **Supported File Types:**
        - PNG (.png)
        - JPEG/JPG (.jpg, .jpeg)
        - GIF (.gif)
        - SVG (.svg)

        **File Organization:**
        - Files stored in date-based structure: YYYY/MM/DD/filename
        - Categories used for metadata organization only
        - Automatic directory creation by upload date
        - Unique filename generation with timestamp

        **Categories (for metadata):**
        - leagues: League logos and images
        - teams: Team logos and images
        - flags: Country flags
        - venues: Stadium and venue images
        - general: General purpose images

        **Example Request:**
        \`\`\`
        POST /upload/file HTTP/1.1
        Authorization: Bearer YOUR_ACCESS_TOKEN
        Content-Type: multipart/form-data

        file: [binary data]
        category: leagues
        description: Premier League official logo
        \`\`\`

        **File Storage Structure:**
        \`\`\`
        IMAGE_STORAGE_PATH/
        └── 2025/
            └── 05/
                └── 25/
                    ├── premier-league-logo-1640995200000.png
                    ├── manchester-united-logo-1640995300000.jpg
                    └── england-flag-1640995400000.svg
        \`\`\`
        `
    })
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                    description: 'Image file to upload'
                },
                category: {
                    type: 'string',
                    enum: Object.values(ImageCategory),
                    description: 'Category for organizing the image'
                },
                description: {
                    type: 'string',
                    description: 'Optional description for the image',
                    maxLength: 255
                }
            },
            required: ['file', 'category']
        }
    })
    @ApiResponse({
        status: 201,
        description: 'Image uploaded successfully',
        type: UploadImageResponseDto,
        example: {
            id: 'img_a1b2c3d4e5f6g7h8',
            originalName: 'premier-league-logo.png',
            filename: '2025/05/25/premier-league-logo-1640995200000.png',
            size: 15420,
            mimeType: 'image/png',
            category: 'leagues',
            url: 'http://localhost:3000/uploads/2025/05/25/premier-league-logo-1640995200000.png',
            path: './public/images/2025/05/25/premier-league-logo-1640995200000.png',
            uploadedAt: '2025-05-25T10:30:00.000Z',
            uploadedBy: 1,
            description: 'Premier League official logo'
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid file or request data',
        example: {
            message: 'Invalid file type. Only PNG, JPG, JPEG, GIF, SVG are allowed.',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Authentication required',
        example: {
            message: 'System authentication required',
            error: 'Unauthorized',
            statusCode: 401
        }
    })
    @ApiResponse({
        status: 413,
        description: 'File too large',
        example: {
            message: 'File size too large. Maximum 10MB allowed.',
            error: 'Payload Too Large',
            statusCode: 413
        }
    })
    @Post('file')
    @UseInterceptors(FileInterceptor('file'))
    async uploadFile(
        @UploadedFile() file: Express.Multer.File,
        @Body('category', new ParseEnumPipe(ImageCategory)) category: ImageCategory,
        @GetCurrentUser() user: SystemUser,
        @Body('description') description?: string,
    ): Promise<UploadImageResponseDto> {
        if (!file) {
            throw new BadRequestException('No file provided');
        }

        return this.uploadService.uploadImageFromFile(file, category, user.id, description);
    }

    @ApiOperation({
        summary: 'Upload Image from URL (SystemUser Only)',
        description: `
        Upload an image from a remote URL with automatic download and storage.

        **🔒 AUTHENTICATION REQUIRED:**
        SystemUser authentication required (Admin/Moderator/Editor roles accepted).

        **Features:**
        - Remote image download
        - URL validation
        - File type validation
        - Size limit: 10MB maximum
        - Custom filename support
        - Category-based organization
        - Audit logging

        **Supported Image URLs:**
        - Direct image links (PNG, JPG, JPEG, GIF, SVG)
        - HTTPS and HTTP protocols
        - Maximum file size: 10MB
        - Timeout: 30 seconds

        **Example Request:**
        \`\`\`
        POST /upload/url HTTP/1.1
        Authorization: Bearer YOUR_ACCESS_TOKEN
        Content-Type: application/json

        {
          "imageUrl": "https://media.api-sports.io/football/leagues/39.png",
          "category": "leagues",
          "filename": "premier-league-logo",
          "description": "Premier League official logo"
        }
        \`\`\`
        `
    })
    @ApiBody({
        type: UploadImageByUrlDto,
        examples: {
            leagueLogo: {
                summary: 'League Logo Example',
                value: {
                    imageUrl: 'https://media.api-sports.io/football/leagues/39.png',
                    category: 'leagues',
                    filename: 'premier-league-logo',
                    description: 'Premier League official logo'
                }
            },
            teamLogo: {
                summary: 'Team Logo Example',
                value: {
                    imageUrl: 'https://media.api-sports.io/football/teams/33.png',
                    category: 'teams',
                    filename: 'manchester-united-logo',
                    description: 'Manchester United official logo'
                }
            },
            countryFlag: {
                summary: 'Country Flag Example',
                value: {
                    imageUrl: 'https://media.api-sports.io/flags/gb.svg',
                    category: 'flags',
                    filename: 'england-flag',
                    description: 'England national flag'
                }
            }
        }
    })
    @ApiResponse({
        status: 201,
        description: 'Image uploaded from URL successfully',
        type: UploadImageResponseDto
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid URL or image type',
        example: {
            message: 'Unable to download image from the provided URL.',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Authentication required',
        example: {
            message: 'System authentication required',
            error: 'Unauthorized',
            statusCode: 401
        }
    })
    @Post('url')
    async uploadFromUrl(
        @Body() uploadDto: UploadImageByUrlDto,
        @GetCurrentUser() user: SystemUser,
    ): Promise<UploadImageResponseDto> {
        return this.uploadService.uploadImageFromUrl(uploadDto, user.id);
    }

    @ApiOperation({
        summary: 'Get Uploaded Images (SystemUser Only)',
        description: `
        Retrieve uploaded images with pagination and filtering options.

        **Features:**
        - Pagination support
        - Category filtering
        - Search by filename or description
        - Sorted by upload date (newest first)
        - Complete image metadata

        **Query Parameters:**
        - page: Page number (default: 1)
        - limit: Items per page (default: 20)
        - category: Filter by image category
        - search: Search in filename or description
        `
    })
    @ApiQuery({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number',
        example: 1
    })
    @ApiQuery({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page',
        example: 20
    })
    @ApiQuery({
        name: 'category',
        required: false,
        enum: ImageCategory,
        description: 'Filter by category'
    })
    @ApiQuery({
        name: 'search',
        required: false,
        type: String,
        description: 'Search by filename or description'
    })
    @ApiResponse({
        status: 200,
        description: 'Images retrieved successfully',
        type: ImageListResponseDto
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Authentication required'
    })
    @Get()
    async getImages(@Query() query: GetImagesDto): Promise<ImageListResponseDto> {
        return this.uploadService.getImages(query);
    }

    @ApiOperation({
        summary: 'Get Image by ID (SystemUser Only)',
        description: 'Retrieve detailed information about a specific uploaded image.'
    })
    @ApiParam({
        name: 'imageId',
        type: 'string',
        description: 'Unique image identifier',
        example: 'img_a1b2c3d4e5f6g7h8'
    })
    @ApiResponse({
        status: 200,
        description: 'Image details retrieved successfully',
        type: UploadImageResponseDto
    })
    @ApiResponse({
        status: 404,
        description: 'Image not found'
    })
    @Get(':imageId')
    async getImageById(@Param('imageId') imageId: string): Promise<UploadImageResponseDto> {
        return this.uploadService.getImageById(imageId);
    }

    @ApiOperation({
        summary: 'Delete Image (SystemUser Only)',
        description: `
        Delete an uploaded image from both database and filesystem.

        **Features:**
        - Complete image removal
        - File system cleanup
        - Database record deletion
        - Audit logging

        **Note:** This action is irreversible.
        `
    })
    @ApiParam({
        name: 'imageId',
        type: 'string',
        description: 'Unique image identifier',
        example: 'img_a1b2c3d4e5f6g7h8'
    })
    @ApiResponse({
        status: 204,
        description: 'Image deleted successfully'
    })
    @ApiResponse({
        status: 404,
        description: 'Image not found'
    })
    @Delete(':imageId')
    async deleteImage(
        @Param('imageId') imageId: string,
        @GetCurrentUser() user: SystemUser,
    ): Promise<void> {
        await this.uploadService.deleteImage(imageId, user.id);
    }
}
