import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BroadcastLinkService } from './broadcast-link.service';
import { BroadcastLinkController, PublicBroadcastLinkController } from './broadcast-link.controller';
import { BroadcastLink } from './broadcast-link.entity';
import { Fixture } from '../sports/football/models/fixture.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([BroadcastLink, Fixture]),
    ],
    controllers: [BroadcastLinkController, PublicBroadcastLinkController],
    providers: [BroadcastLinkService],
    exports: [BroadcastLinkService],
})
export class BroadcastLinkModule { }