import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

@Entity('broadcast_links')
export class BroadcastLink {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column()
    fixtureId: number;

    @Column()
    linkName: string;

    @Column()
    linkUrl: string;

    @Column()
    linkComment: string;

    @Column({ nullable: true })
    language?: string;

    @Column({ nullable: true })
    quality?: string;

    @Column()
    addedBy: number;

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}