import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddThumbToFixture1748190000000 implements MigrationInterface {
    name = 'AddThumbToFixture1748190000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn('fixtures', new TableColumn({
            name: 'thumb',
            type: 'varchar',
            length: '500',
            isNullable: true,
            comment: 'Thumbnail image path for the fixture'
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('fixtures', 'thumb');
    }
}
