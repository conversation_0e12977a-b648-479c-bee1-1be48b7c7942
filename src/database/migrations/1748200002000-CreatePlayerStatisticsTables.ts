import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreatePlayerStatisticsTables1748200002000 implements MigrationInterface {
    name = 'CreatePlayerStatisticsTables1748200002000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create player_statistics table
        await queryRunner.createTable(
            new Table({
                name: 'player_statistics',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'playerId',
                        type: 'int',
                    },
                    {
                        name: 'teamId',
                        type: 'int',
                    },
                    {
                        name: 'teamName',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'teamLogo',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'leagueId',
                        type: 'int',
                    },
                    {
                        name: 'leagueName',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'leagueCountry',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'leagueLogo',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'season',
                        type: 'int',
                    },
                    {
                        name: 'games',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'substitutes',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'shots',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'goals',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'passes',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'tackles',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'duels',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'dribbles',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'fouls',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'cards',
                        type: 'jsonb',
                        isNullable: true,
                    },
                    {
                        name: 'penalty',
                        type: 'jsonb',
                        isNullable: true,
                    },
                ],
            }),
            true,
        );

        // Create indexes
        await queryRunner.createIndex('player_statistics', {
            name: 'IDX_PLAYER_STATS_PLAYER',
            columnNames: ['playerId']
        });
        await queryRunner.createIndex('player_statistics', {
            name: 'IDX_PLAYER_STATS_TEAM',
            columnNames: ['teamId']
        });
        await queryRunner.createIndex('player_statistics', {
            name: 'IDX_PLAYER_STATS_LEAGUE',
            columnNames: ['leagueId']
        });
        await queryRunner.createIndex('player_statistics', {
            name: 'IDX_PLAYER_STATS_SEASON',
            columnNames: ['season']
        });

        // Create unique constraint
        await queryRunner.createIndex('player_statistics', {
            name: 'UQ_PLAYER_TEAM_LEAGUE_SEASON',
            columnNames: ['playerId', 'teamId', 'leagueId', 'season'],
            isUnique: true
        });

        // Create foreign key
        await queryRunner.createForeignKey('player_statistics', {
            columnNames: ['playerId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'players',
            onDelete: 'CASCADE',
        });
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('player_statistics');
    }
}
