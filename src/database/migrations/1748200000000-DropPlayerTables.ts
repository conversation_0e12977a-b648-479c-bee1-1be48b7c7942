import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropPlayerTables1748200000000 implements MigrationInterface {
    name = 'DropPlayerTables1748200000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Drop player_statistics table first (has foreign key to players)
        await queryRunner.query(`DROP TABLE IF EXISTS "player_statistics" CASCADE`);
        
        // Drop players table
        await queryRunner.query(`DROP TABLE IF EXISTS "players" CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // This migration is irreversible - we're starting fresh
        // If needed, the tables will be recreated by new migrations
    }
}
