import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreatePlayerTables1748200001000 implements MigrationInterface {
    name = 'CreatePlayerTables1748200001000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create players table
        await queryRunner.createTable(
            new Table({
                name: 'players',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'externalId',
                        type: 'int',
                        isUnique: true,
                    },
                    {
                        name: 'name',
                        type: 'varchar',
                    },
                    {
                        name: 'firstName',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'lastName',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'age',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'birthDate',
                        type: 'date',
                        isNullable: true,
                    },
                    {
                        name: 'birthPlace',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'birthCountry',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'nationality',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'height',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'weight',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'injured',
                        type: 'boolean',
                        default: false,
                    },
                    {
                        name: 'photo',
                        type: 'varchar',
                        isNullable: true,
                    },
                    {
                        name: 'timestamp',
                        type: 'bigint',
                        default: 'EXTRACT(epoch FROM NOW()) * 1000',
                    },
                ],
            }),
            true,
        );

        // Create indexes for players table
        await queryRunner.createIndex('players', {
            name: 'IDX_PLAYER_EXTERNAL_ID',
            columnNames: ['externalId']
        });
        await queryRunner.createIndex('players', {
            name: 'IDX_PLAYER_NAME',
            columnNames: ['name']
        });
        await queryRunner.createIndex('players', {
            name: 'IDX_PLAYER_TIMESTAMP',
            columnNames: ['timestamp']
        });
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('players');
    }
}
