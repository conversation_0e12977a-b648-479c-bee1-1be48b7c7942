import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddIsHotToLeague1748190001000 implements MigrationInterface {
    name = 'AddIsHotToLeague1748190001000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add isHot column to leagues table
        await queryRunner.addColumn('leagues', new TableColumn({
            name: 'isHot',
            type: 'boolean',
            default: false,
            isNullable: false,
            comment: 'Indicates if the league is marked as hot/popular'
        }));

        // Add index for isHot column for better query performance
        await queryRunner.query(`CREATE INDEX "IDX_leagues_isHot" ON "leagues" ("isHot")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop index first
        await queryRunner.query(`DROP INDEX "IDX_leagues_isHot"`);

        // Drop column
        await queryRunner.dropColumn('leagues', 'isHot');
    }
}
