import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBool<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SystemRole } from '../../core/types/auth.types';

/**
 * System User Login DTO
 */
export class SystemUserLoginDto {
    @ApiProperty({
        description: 'Username for system user',
        example: 'admin'
    })
    @IsString()
    @MinLength(3)
    @MaxLength(50)
    username: string;

    @ApiProperty({
        description: 'Password for system user',
        example: 'admin123456'
    })
    @IsString()
    @MinLength(8)
    password: string;
}

/**
 * System User Create DTO
 */
export class SystemUserCreateDto {
    @ApiProperty({
        description: 'Username for new system user',
        example: 'editor1'
    })
    @IsString()
    @MinLength(3)
    @MaxLength(50)
    username: string;

    @ApiProperty({
        description: 'Email for new system user',
        example: '<EMAIL>'
    })
    @IsEmail()
    email: string;

    @ApiProperty({
        description: 'Password for new system user',
        example: 'SecurePassword123!'
    })
    @IsString()
    @MinLength(8)
    password: string;

    @ApiProperty({
        description: 'Role for new system user',
        enum: SystemRole,
        example: SystemRole.EDITOR
    })
    @IsEnum(SystemRole)
    role: SystemRole;

    @ApiProperty({
        description: 'Full name of the system user',
        example: 'John Editor',
        required: false
    })
    @IsOptional()
    @IsString()
    @MaxLength(100)
    fullName?: string;
}

/**
 * Device Info DTO
 */
export class DeviceInfoDto {
    @ApiProperty({
        description: 'Device information',
        example: 'Chrome on Windows',
        required: false
    })
    @IsOptional()
    @IsString()
    deviceInfo?: string;

    @ApiProperty({
        description: 'IP address of the client',
        example: '*************',
        required: false
    })
    @IsOptional()
    @IsString()
    ipAddress?: string;

    @ApiProperty({
        description: 'User agent string',
        example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        required: false
    })
    @IsOptional()
    @IsString()
    userAgent?: string;
}

/**
 * Refresh Token DTO
 */
export class RefreshTokenDto {
    @ApiProperty({
        description: 'Refresh token string',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    })
    @IsString()
    refreshToken: string;
}

/**
 * Token Pair DTO
 */
export class TokenPairDto {
    @ApiProperty({
        description: 'JWT access token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    })
    accessToken: string;

    @ApiProperty({
        description: 'JWT refresh token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    })
    refreshToken: string;
}

/**
 * System Auth Response DTO
 */
export class SystemAuthResponseDto {
    @ApiProperty({
        description: 'JWT access token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    })
    accessToken: string;

    @ApiProperty({
        description: 'JWT refresh token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    })
    refreshToken: string;

    @ApiProperty({
        description: 'System user information'
    })
    user: {
        id: number;
        username: string;
        email: string;
        role: SystemRole;
        fullName?: string;
        isActive: boolean;
        lastLoginAt?: Date;
        createdAt: Date;
    };
}

/**
 * System User Profile DTO
 */
export class SystemUserProfileDto {
    @ApiProperty({ description: 'User ID', example: 1 })
    id: number;

    @ApiProperty({ description: 'Username', example: 'admin' })
    username: string;

    @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
    email: string;

    @ApiProperty({ description: 'Full name', example: 'System Administrator', required: false })
    fullName?: string;

    @ApiProperty({ description: 'User role', enum: SystemRole, example: SystemRole.ADMIN })
    role: SystemRole;

    @ApiProperty({ description: 'Account status', example: true })
    isActive: boolean;

    @ApiProperty({ description: 'Last login timestamp', example: '2024-01-15T10:30:00Z', required: false })
    lastLoginAt?: Date;

    @ApiProperty({ description: 'Account creation timestamp', example: '2024-01-01T00:00:00Z' })
    createdAt: Date;

    @ApiProperty({ description: 'Last update timestamp', example: '2024-01-15T10:30:00Z' })
    updatedAt: Date;
}

/**
 * System User Update DTO
 */
export class SystemUserUpdateDto {
    @ApiProperty({
        description: 'Email for system user',
        example: '<EMAIL>',
        required: false
    })
    @IsEmail()
    @IsOptional()
    email?: string;

    @ApiProperty({
        description: 'Full name of the system user',
        example: 'John Updated Editor',
        required: false
    })
    @IsString()
    @MaxLength(100)
    @IsOptional()
    fullName?: string;

    @ApiProperty({
        description: 'Role for system user (admin only)',
        enum: SystemRole,
        example: SystemRole.EDITOR,
        required: false
    })
    @IsEnum(SystemRole)
    @IsOptional()
    role?: SystemRole;

    @ApiProperty({
        description: 'Active status (admin only)',
        example: true,
        required: false
    })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}

/**
 * System User Change Password DTO
 */
export class SystemUserChangePasswordDto {
    @ApiProperty({
        description: 'Current password',
        example: 'CurrentPassword123!'
    })
    @IsString()
    @MinLength(8)
    currentPassword: string;

    @ApiProperty({
        description: 'New password',
        example: 'NewSecurePassword123!'
    })
    @IsString()
    @MinLength(8)
    newPassword: string;

    @ApiProperty({
        description: 'Confirm new password',
        example: 'NewSecurePassword123!'
    })
    @IsString()
    @MinLength(8)
    confirmPassword: string;
}
