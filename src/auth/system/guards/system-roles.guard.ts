import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY, ADMIN_ONLY_KEY, EDITOR_PLUS_KEY } from '../../core/decorators/auth.decorators';
import { SystemRole } from '../../core/types/auth.types';

/**
 * System Roles Guard
 * Enforces role-based access control for system users
 */
@Injectable()
export class SystemRolesGuard implements CanActivate {
    constructor(private reflector: Reflector) { }

    canActivate(context: ExecutionContext): boolean {
        // Check for specific role decorators
        const isAdminOnly = this.reflector.getAllAndOverride<boolean>(ADMIN_ONLY_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        const isEditorPlus = this.reflector.getAllAndOverride<boolean>(EDITOR_PLUS_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        const requiredRoles = this.reflector.getAllAndOverride<SystemRole[]>(ROLES_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        // If no role requirements, allow access
        if (!isAdminOnly && !isEditorPlus && !requiredRoles) {
            return true;
        }

        const { user } = context.switchToHttp().getRequest();

        if (!user) {
            throw new ForbiddenException('User not authenticated');
        }

        // Ensure this is a system user
        if (user.userType !== 'system') {
            throw new ForbiddenException('System user access required');
        }

        const userRole = user.role as SystemRole;

        // Check admin only access
        if (isAdminOnly) {
            if (userRole !== SystemRole.ADMIN) {
                throw new ForbiddenException('Admin access required');
            }
            return true;
        }

        // Check editor plus access (editor or admin)
        if (isEditorPlus) {
            if (userRole !== SystemRole.EDITOR && userRole !== SystemRole.ADMIN) {
                throw new ForbiddenException('Editor or Admin access required');
            }
            return true;
        }

        // Check specific roles
        if (requiredRoles) {
            if (!requiredRoles.includes(userRole)) {
                throw new ForbiddenException(`Required roles: ${requiredRoles.join(', ')}`);
            }
            return true;
        }

        return true;
    }
}
