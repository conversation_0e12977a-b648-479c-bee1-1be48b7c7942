import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../../core/decorators/auth.decorators';

/**
 * System JWT Authentication Guard
 * Protects routes that require system user authentication
 */
@Injectable()
export class SystemJwtAuthGuard extends AuthGuard('system-jwt') {
    constructor(private reflector: Reflector) {
        super();
    }

    canActivate(context: ExecutionContext) {
        const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        if (isPublic) {
            return true;
        }

        return super.canActivate(context);
    }

    handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
        if (err || !user) {
            throw err || new UnauthorizedException('System authentication required');
        }

        // Ensure this is a system user
        if (user.userType !== 'system') {
            throw new UnauthorizedException('System user access required');
        }

        return user;
    }
}
