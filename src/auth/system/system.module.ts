import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Entities
import { SystemUser } from './entities/system-user.entity';
import { SystemRefreshToken } from './entities/system-refresh-token.entity';

// Services
import { SystemAuthService } from './services/system-auth.service';
import { AdminSeederService } from './services/admin-seeder.service';

// Controllers
import { SystemAuthController } from './controllers/system-auth.controller';

// Strategies
import { SystemJwtStrategy } from './strategies/system-jwt.strategy';

// Guards
import { SystemJwtAuthGuard } from './guards/system-jwt-auth.guard';
import { SystemRolesGuard } from './guards/system-roles.guard';

// Shared services
import { AuditLogService } from '../shared/services/audit-log.service';

/**
 * System Module
 * Handles authentication and authorization for system users (admin, editor)
 */
@Module({
    imports: [
        // Database entities
        TypeOrmModule.forFeature([
            SystemUser,
            SystemRefreshToken,
        ]),

        // JWT configuration
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                secret: configService.get<string>('JWT_SECRET', 'default-secret'),
                signOptions: {
                    expiresIn: configService.get<string>('JWT_ACCESS_EXPIRES_IN', '15m'),
                },
            }),
            inject: [ConfigService],
        }),

        // Passport configuration
        PassportModule.register({ defaultStrategy: 'system-jwt' }),

        // Configuration
        ConfigModule,
    ],

    controllers: [
        SystemAuthController,
    ],

    providers: [
        // Services
        SystemAuthService,
        AdminSeederService,
        AuditLogService,

        // Strategies
        SystemJwtStrategy,

        // Guards
        SystemJwtAuthGuard,
        SystemRolesGuard,
    ],

    exports: [
        // Export services for use in other modules
        SystemAuthService,

        // Export guards for use in other modules
        SystemJwtAuthGuard,
        SystemRolesGuard,

        // Export strategy
        SystemJwtStrategy,

        // Export TypeORM repositories
        TypeOrmModule,
    ],
})
export class SystemModule { }
