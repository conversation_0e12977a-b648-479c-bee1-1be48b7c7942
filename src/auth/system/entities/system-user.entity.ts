import { Entity, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * System User Entity
 * For internal users with administrative privileges
 */
@Entity('system_users')
export class SystemUser {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({ unique: true })
    username: string;

    @Index()
    @Column({ unique: true })
    email: string;

    @Column()
    passwordHash: string;

    @Column({ nullable: true })
    fullName: string;

    @Column({ type: 'enum', enum: ['admin', 'editor', 'moderator'], default: 'editor' })
    role: 'admin' | 'editor' | 'moderator';

    @Column({ default: true })
    isActive: boolean;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    lastLoginAt: Date;

    @Column({ nullable: true })
    createdBy: number; // ID of admin who created this user

    @CreateDateColumn({ type: 'timestamp with time zone' })
    createdAt: Date;

    @UpdateDateColumn({ type: 'timestamp with time zone' })
    updatedAt: Date;

    /**
     * Check if user has admin privileges
     */
    isAdmin(): boolean {
        return this.role === 'admin' && this.isActive;
    }

    /**
     * Check if user can edit content
     */
    canEditContent(): boolean {
        return ['admin', 'editor'].includes(this.role) && this.isActive;
    }

    /**
     * Check if user can moderate content
     */
    canModerate(): boolean {
        return ['admin', 'moderator'].includes(this.role) && this.isActive;
    }
}
