import {
    Controller,
    Post,
    Body,
    HttpCode,
    HttpStatus,
    UseGuards,
    Req,
    Get,
    Put,
    Param,
    ParseIntPipe
} from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { SystemAuthService } from '../services/system-auth.service';
import {
    SystemUserLoginDto,
    SystemUserCreateDto,
    RefreshTokenDto,
    TokenPairDto,
    SystemAuthResponseDto,
    SystemUserProfileDto,
    DeviceInfoDto,
    SystemUserUpdateDto,
    SystemUserChangePasswordDto
} from '../dto/system-auth.dto';
import { SystemJwtAuthGuard } from '../guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../guards/system-roles.guard';
import { AdminOnly, GetCurrentUser } from '../../core/decorators/auth.decorators';
import { SystemUser } from '../entities/system-user.entity';
import { SystemRole } from '../../core/types/auth.types';

/**
 * System Authentication Controller
 * Handles authentication for system users with 3-tier role system
 */
@ApiTags('System Authentication')
@Controller('system-auth')
export class SystemAuthController {
    constructor(private readonly systemAuthService: SystemAuthService) { }

    @ApiOperation({
        summary: 'System User Login',
        description: `
        Login endpoint for system users with 3-tier role system.

        **User Roles:**
        - **Admin**: Full system access, user management, all operations
        - **Moderator**: Content moderation, full BroadcastLink access, user monitoring
        - **Editor**: Content creation, own BroadcastLink management only

        **Features:**
        - Username/password authentication
        - JWT token generation with role information
        - Device tracking and session management
        - Comprehensive audit logging
        - Refresh token support

        **Security:**
        - Rate limiting applied (5 attempts per minute)
        - Password validation and hashing
        - Account status verification
        - Login attempt logging and monitoring

        **Examples:**
        - Admin: username: "admin", password: "admin123456"
        - Editor: username: "editor1", password: "editor123456"
        - Moderator: username: "moderator1", password: "moderator123456"
        `
    })
    @ApiBody({ type: SystemUserLoginDto })
    @ApiResponse({
        status: 200,
        description: 'Login successful',
        type: SystemAuthResponseDto
    })
    @ApiResponse({
        status: 401,
        description: 'Invalid credentials'
    })
    @ApiResponse({
        status: 429,
        description: 'Too many login attempts'
    })
    @Post('login')
    @HttpCode(HttpStatus.OK)
    async login(
        @Body() loginDto: SystemUserLoginDto,
        @Req() req: Request
    ): Promise<TokenPairDto> {
        const deviceInfo: DeviceInfoDto = {
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            deviceInfo: `${req.get('User-Agent')?.split(' ')[0]} on ${req.get('User-Agent')?.includes('Windows') ? 'Windows' : 'Unknown'}`
        };

        return this.systemAuthService.login(loginDto, deviceInfo);
    }

    @ApiOperation({
        summary: 'Create System User (Admin Only)',
        description: `
        Create a new system user account with role-based permissions.

        **Access:** Admin only
        **Features:**
        - Create users with different roles
        - Password hashing and validation
        - Email uniqueness validation
        - Username uniqueness check
        - Comprehensive audit logging

        **Available Roles:**
        - **admin**: Full system access, user management, all operations
        - **moderator**: Content moderation, full BroadcastLink access, user monitoring
        - **editor**: Content creation, own BroadcastLink management only

        **Role Permissions:**
        - Admin: Can create/edit/delete any user, full system control
        - Moderator: Can moderate content, manage all BroadcastLinks, view users
        - Editor: Can create content, manage own BroadcastLinks only

        **Examples:**
        - Create Admin: {"username": "admin2", "email": "<EMAIL>", "password": "admin123456", "role": "admin"}
        - Create Moderator: {"username": "mod1", "email": "<EMAIL>", "password": "mod123456", "role": "moderator"}
        - Create Editor: {"username": "editor2", "email": "<EMAIL>", "password": "editor123456", "role": "editor"}
        `
    })
    @ApiBody({ type: SystemUserCreateDto })
    @ApiResponse({
        status: 201,
        description: 'System user created successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 409,
        description: 'Username or email already exists'
    })
    @ApiResponse({
        status: 403,
        description: 'Admin access required'
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
    @AdminOnly()
    @Post('create-user')
    async createUser(@Body() createUserDto: SystemUserCreateDto): Promise<{
        message: string;
        user: SystemUserProfileDto
    }> {
        const user = await this.systemAuthService.createUser(createUserDto);

        return {
            message: 'System user created successfully',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                fullName: user.fullName,
                role: user.role as SystemRole,
                isActive: user.isActive,
                lastLoginAt: user.lastLoginAt,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }
        };
    }

    @ApiOperation({
        summary: 'Refresh Access Token',
        description: `
        Refresh an expired access token using a valid refresh token.

        **Features:**
        - Refresh token validation
        - New access token generation
        - Token expiry checking
        - User status verification
        `
    })
    @ApiBody({ type: RefreshTokenDto })
    @ApiResponse({
        status: 200,
        description: 'Access token refreshed successfully',
        schema: {
            type: 'object',
            properties: {
                accessToken: {
                    type: 'string',
                    description: 'New JWT access token'
                }
            }
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Invalid or expired refresh token'
    })
    @Post('refresh')
    @HttpCode(HttpStatus.OK)
    async refreshToken(@Body() refreshTokenDto: RefreshTokenDto): Promise<{ accessToken: string }> {
        return this.systemAuthService.refreshAccessToken(refreshTokenDto.refreshToken);
    }

    @ApiOperation({
        summary: 'Logout',
        description: `
        Logout current session by revoking the refresh token.

        **Features:**
        - Refresh token revocation
        - Session termination
        - Audit logging
        `
    })
    @ApiBody({ type: RefreshTokenDto })
    @ApiResponse({
        status: 200,
        description: 'Logout successful',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Logout successful'
                }
            }
        }
    })
    @Post('logout')
    @HttpCode(HttpStatus.OK)
    async logout(@Body() refreshTokenDto: RefreshTokenDto): Promise<{ message: string }> {
        await this.systemAuthService.logout(refreshTokenDto.refreshToken);
        return { message: 'Logout successful' };
    }

    @ApiOperation({
        summary: 'Logout from All Devices',
        description: `
        Logout from all devices by revoking all refresh tokens for the current user.

        **Features:**
        - All refresh tokens revocation
        - Multi-device session termination
        - Security enhancement
        `
    })
    @ApiResponse({
        status: 200,
        description: 'Logged out from all devices successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Logged out from all devices successfully'
                }
            }
        }
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard)
    @Post('logout-all')
    @HttpCode(HttpStatus.OK)
    async logoutFromAllDevices(@GetCurrentUser() user: SystemUser): Promise<{ message: string }> {
        await this.systemAuthService.logoutFromAllDevices(user.id);
        return { message: 'Logged out from all devices successfully' };
    }

    @ApiOperation({
        summary: 'Get Current User Profile',
        description: `
        Get the profile information of the currently authenticated system user.

        **Features:**
        - Current user information
        - Role and permissions
        - Account status
        - Last login information
        `
    })
    @ApiResponse({
        status: 200,
        description: 'User profile retrieved successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 401,
        description: 'Authentication required'
    })
    @ApiOperation({
        summary: 'Get Current User Profile (Test Authentication)',
        description: `
        Retrieve the profile information of the currently authenticated system user.

        **🔒 AUTHENTICATION REQUIRED:**
        Any authenticated SystemUser (Admin/Moderator/Editor).

        **🧪 PERFECT FOR TESTING SWAGGER UI AUTHENTICATION:**
        This endpoint is ideal for testing if your authentication is working in Swagger UI.

        **Step-by-Step Authentication Test:**
        1. **Login**: POST /system-auth/login
        2. **Copy Token**: Copy "accessToken" from response
        3. **Authorize**: Click "Authorize" button (🔓) at top of Swagger UI
        4. **Enter Token**: Enter exactly: Bearer YOUR_ACCESS_TOKEN (with space after Bearer)
        5. **Authorize**: Click "Authorize" and "Close"
        6. **Test**: Try this endpoint - should return your user profile

        **Test Credentials:**
        - Admin: {"username": "admin", "password": "admin123456"}
        - Editor: {"username": "editor1", "password": "editor123456"}
        - Moderator: {"username": "moderator1", "password": "moderator123456"}

        **Expected Results:**
        - ✅ **Success**: Returns your user profile with role information
        - ❌ **401 Error**: Token not sent properly - check authorization setup
        - ❌ **403 Error**: Token invalid or expired - login again

        **Example Request:**
        \`\`\`
        GET /system-auth/profile HTTP/1.1
        Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        \`\`\`

        **Troubleshooting:**
        - Make sure token starts with "Bearer " (with space)
        - Check token hasn't expired (1 hour expiration)
        - Refresh page and re-authorize if needed
        - Verify you clicked "Authorize" and "Close" buttons
        `
    })
    @ApiResponse({
        status: 200,
        description: 'User profile retrieved successfully',
        type: SystemUserProfileDto,
        example: {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            fullName: 'System Administrator',
            role: 'admin',
            isActive: true,
            lastLoginAt: '2025-05-25T10:30:00.000Z',
            createdAt: '2025-01-01T00:00:00.000Z',
            updatedAt: '2025-05-25T10:30:00.000Z'
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Token not provided or invalid',
        example: {
            message: 'System authentication required',
            error: 'Unauthorized',
            statusCode: 401,
            help: 'Make sure you have authorized in Swagger UI with Bearer token'
        }
    })
    @ApiBearerAuth('bearer')
    @UseGuards(SystemJwtAuthGuard)
    @Get('profile')
    async getProfile(@GetCurrentUser() user: SystemUser): Promise<SystemUserProfileDto> {
        return {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role as SystemRole,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
    }

    @ApiOperation({
        summary: 'Update User Profile',
        description: `
        Update system user profile information.

        **Features:**
        - Update email and full name (all users)
        - Update role and active status (admin only)
        - Email uniqueness validation
        - Permission-based field updates

        **Permissions:**
        - All users: Can update email, fullName
        - Admin only: Can update role, isActive

        **Security:**
        - Email uniqueness check
        - Role-based access control
        - Audit logging
        `
    })
    @ApiBody({ type: SystemUserUpdateDto })
    @ApiResponse({
        status: 200,
        description: 'User profile updated successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 403,
        description: 'Forbidden - Insufficient permissions'
    })
    @ApiResponse({
        status: 409,
        description: 'Email already exists'
    })
    @ApiBearerAuth('bearer')
    @UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
    @Put('profile')
    async updateProfile(
        @Body() updateDto: SystemUserUpdateDto,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{
        message: string;
        user: SystemUserProfileDto
    }> {
        const updatedUser = await this.systemAuthService.updateUser(currentUser.id, updateDto, currentUser);

        return {
            message: 'Profile updated successfully',
            user: {
                id: updatedUser.id,
                username: updatedUser.username,
                email: updatedUser.email,
                fullName: updatedUser.fullName,
                role: updatedUser.role as SystemRole,
                isActive: updatedUser.isActive,
                lastLoginAt: updatedUser.lastLoginAt,
                createdAt: updatedUser.createdAt,
                updatedAt: updatedUser.updatedAt
            }
        };
    }

    @ApiOperation({
        summary: 'Update User by ID (Admin Only)',
        description: `
        Update any system user profile by ID (admin only).

        **Features:**
        - Update any user's information
        - Full admin control over all fields
        - Email uniqueness validation
        - Audit logging

        **Admin Permissions:**
        - Update email, fullName, role, isActive
        - Manage any user account
        - Activate/deactivate users
        `
    })
    @ApiBody({ type: SystemUserUpdateDto })
    @ApiResponse({
        status: 200,
        description: 'User updated successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 403,
        description: 'Admin access required'
    })
    @ApiResponse({
        status: 404,
        description: 'User not found'
    })
    @ApiResponse({
        status: 409,
        description: 'Email already exists'
    })
    @ApiBearerAuth('bearer')
    @UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
    @AdminOnly()
    @Put('users/:id')
    async updateUser(
        @Param('id', ParseIntPipe) userId: number,
        @Body() updateDto: SystemUserUpdateDto,
        @GetCurrentUser() currentUser: SystemUser
    ): Promise<{
        message: string;
        user: SystemUserProfileDto
    }> {
        const updatedUser = await this.systemAuthService.updateUser(userId, updateDto, currentUser);

        return {
            message: 'User updated successfully',
            user: {
                id: updatedUser.id,
                username: updatedUser.username,
                email: updatedUser.email,
                fullName: updatedUser.fullName,
                role: updatedUser.role as SystemRole,
                isActive: updatedUser.isActive,
                lastLoginAt: updatedUser.lastLoginAt,
                createdAt: updatedUser.createdAt,
                updatedAt: updatedUser.updatedAt
            }
        };
    }

    @ApiOperation({
        summary: 'Change Password',
        description: `
        Change current user's password.

        **Features:**
        - Current password verification
        - New password confirmation
        - Password strength validation
        - Automatic logout from all devices

        **Security:**
        - Current password required
        - Password confirmation match
        - All sessions invalidated after change
        - Audit logging
        `
    })
    @ApiBody({ type: SystemUserChangePasswordDto })
    @ApiResponse({
        status: 200,
        description: 'Password changed successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Password changed successfully. Please login again.'
                }
            }
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid password or confirmation mismatch'
    })
    @ApiBearerAuth('bearer')
    @UseGuards(SystemJwtAuthGuard)
    @Post('change-password')
    @HttpCode(HttpStatus.OK)
    async changePassword(
        @Body() changePasswordDto: SystemUserChangePasswordDto,
        @GetCurrentUser() user: SystemUser
    ): Promise<{ message: string }> {
        await this.systemAuthService.changePassword(user.id, changePasswordDto);
        return { message: 'Password changed successfully. Please login again.' };
    }
}
