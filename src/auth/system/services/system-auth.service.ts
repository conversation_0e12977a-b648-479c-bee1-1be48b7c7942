import { Injectable, UnauthorizedException, ConflictException, Logger, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { SystemUser } from '../entities/system-user.entity';
import { SystemRefreshToken } from '../entities/system-refresh-token.entity';
import { SystemUserLoginDto, SystemUserCreateDto, TokenPairDto, DeviceInfoDto, SystemUserUpdateDto, SystemUserChangePasswordDto } from '../dto/system-auth.dto';
import { SystemRole, UserType, SystemUserJwtPayload } from '../../core/types/auth.types';
import { AuditLogService } from '../../shared/services/audit-log.service';

/**
 * System Authentication Service
 * Handles login, registration, token management for system users (admin, editor)
 */
@Injectable()
export class SystemAuthService {
    private readonly logger = new Logger(SystemAuthService.name);

    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        @InjectRepository(SystemRefreshToken)
        private refreshTokenRepository: Repository<SystemRefreshToken>,
        private jwtService: JwtService,
        private configService: ConfigService,
        private auditLogService: AuditLogService,
    ) { }

    /**
     * System user login
     */
    async login(loginDto: SystemUserLoginDto, deviceInfo?: DeviceInfoDto): Promise<TokenPairDto> {
        this.logger.debug(`Login attempt for system user: ${loginDto.username}`);

        // Find user by username
        const user = await this.systemUserRepository.findOne({
            where: { username: loginDto.username }
        });

        if (!user || !user.isActive) {
            this.logger.warn(`Login failed for: ${loginDto.username} - User not found or inactive`);
            throw new UnauthorizedException('Invalid credentials');
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(loginDto.password, user.passwordHash);
        if (!isPasswordValid) {
            this.logger.warn(`Login failed for: ${loginDto.username} - Invalid password`);
            throw new UnauthorizedException('Invalid credentials');
        }

        // Generate token pair
        const tokens = await this.generateTokenPair(user);

        // Store refresh token
        await this.storeRefreshToken(tokens.refreshToken, user.id, deviceInfo);

        // Update last login
        user.lastLoginAt = new Date();
        await this.systemUserRepository.save(user);

        // Log successful login
        await this.auditLogService.logLogin(user, deviceInfo?.ipAddress, deviceInfo?.userAgent);

        this.logger.log(`System user ${user.username} logged in successfully`);
        return tokens;
    }

    /**
     * Create system user (admin only)
     */
    async createUser(createUserDto: SystemUserCreateDto): Promise<SystemUser> {
        this.logger.debug(`Creating system user: ${createUserDto.username}`);

        // Check if username already exists
        const existingUser = await this.systemUserRepository.findOne({
            where: { username: createUserDto.username }
        });

        if (existingUser) {
            throw new ConflictException('Username already exists');
        }

        // Check if email already exists
        const existingEmail = await this.systemUserRepository.findOne({
            where: { email: createUserDto.email }
        });

        if (existingEmail) {
            throw new ConflictException('Email already exists');
        }

        // Hash password
        const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
        const hashedPassword = await bcrypt.hash(createUserDto.password, saltRounds);

        // Create user
        const user = this.systemUserRepository.create({
            ...createUserDto,
            passwordHash: hashedPassword,
        });

        const savedUser = await this.systemUserRepository.save(user);
        this.logger.log(`System user created successfully: ${savedUser.username} (ID: ${savedUser.id})`);

        return savedUser;
    }

    /**
     * Refresh access token
     */
    async refreshAccessToken(refreshTokenString: string): Promise<{ accessToken: string }> {
        // Find refresh token in database
        const refreshToken = await this.refreshTokenRepository.findOne({
            where: { token: refreshTokenString, isRevoked: false },
            relations: ['systemUser']
        });

        if (!refreshToken || !refreshToken.isValid()) {
            throw new UnauthorizedException('Invalid or expired refresh token');
        }

        // Generate new access token
        const accessToken = await this.generateAccessToken(refreshToken.systemUser);

        this.logger.debug(`Access token refreshed for system user ${refreshToken.systemUser.username}`);
        return { accessToken };
    }

    /**
     * Logout user (revoke refresh token)
     */
    async logout(refreshTokenString: string): Promise<void> {
        const refreshToken = await this.refreshTokenRepository.findOne({
            where: { token: refreshTokenString }
        });

        if (refreshToken) {
            refreshToken.revoke();
            await this.refreshTokenRepository.save(refreshToken);
            this.logger.log(`Refresh token revoked for system user ${refreshToken.userId}`);
        }
    }

    /**
     * Logout from all devices (revoke all refresh tokens for user)
     */
    async logoutFromAllDevices(userId: number): Promise<void> {
        await this.refreshTokenRepository.update(
            { userId, isRevoked: false },
            { isRevoked: true, revokedAt: new Date() }
        );

        this.logger.log(`All refresh tokens revoked for system user ${userId}`);
    }

    /**
     * Generate access and refresh token pair
     */
    private async generateTokenPair(user: SystemUser): Promise<TokenPairDto> {
        const accessToken = await this.generateAccessToken(user);
        const refreshToken = await this.generateRefreshToken(user);

        return { accessToken, refreshToken };
    }

    /**
     * Generate access token (short-lived)
     */
    private async generateAccessToken(user: SystemUser): Promise<string> {
        const payload: SystemUserJwtPayload = {
            sub: user.id,
            username: user.username,
            email: user.email,
            role: user.role as SystemRole,
            userType: UserType.SYSTEM,
        };

        return this.jwtService.signAsync(payload, {
            expiresIn: this.configService.get<string>('JWT_ACCESS_EXPIRES_IN', '15m'),
        });
    }

    /**
     * Generate refresh token (long-lived)
     */
    private async generateRefreshToken(user: SystemUser): Promise<string> {
        const payload = {
            sub: user.id,
            type: 'refresh',
            userType: UserType.SYSTEM,
        };

        return this.jwtService.signAsync(payload, {
            secret: this.configService.get<string>('JWT_REFRESH_SECRET', 'default-refresh-secret'),
            expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d'),
        });
    }

    /**
     * Store refresh token in database
     */
    private async storeRefreshToken(
        token: string,
        userId: number,
        deviceInfo?: DeviceInfoDto
    ): Promise<void> {
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

        const refreshToken = this.refreshTokenRepository.create({
            token,
            userId,
            deviceInfo: deviceInfo?.deviceInfo,
            ipAddress: deviceInfo?.ipAddress,
            userAgent: deviceInfo?.userAgent,
            expiresAt,
        });

        await this.refreshTokenRepository.save(refreshToken);
    }

    /**
     * Clean up expired refresh tokens
     */
    async cleanupExpiredTokens(): Promise<void> {
        const result = await this.refreshTokenRepository.delete({
            expiresAt: { $lt: new Date() } as any
        });

        this.logger.log(`Cleaned up ${result.affected} expired system refresh tokens`);
    }

    /**
     * Update system user profile
     */
    async updateUser(userId: number, updateDto: SystemUserUpdateDto, currentUser: SystemUser): Promise<SystemUser> {
        this.logger.debug(`Updating system user: ${userId}`);

        // Find user to update
        const user = await this.systemUserRepository.findOne({
            where: { id: userId }
        });

        if (!user) {
            throw new UnauthorizedException('User not found');
        }

        // Check permissions for role and isActive changes
        if ((updateDto.role || updateDto.isActive !== undefined) && !currentUser.isAdmin()) {
            throw new ForbiddenException('Only admin can change role or active status');
        }

        // Check if email already exists (if changing email)
        if (updateDto.email && updateDto.email !== user.email) {
            const existingEmail = await this.systemUserRepository.findOne({
                where: { email: updateDto.email }
            });

            if (existingEmail) {
                throw new ConflictException('Email already exists');
            }
        }

        // Update user fields
        if (updateDto.email) user.email = updateDto.email;
        if (updateDto.fullName !== undefined) user.fullName = updateDto.fullName;
        if (updateDto.role && currentUser.isAdmin()) user.role = updateDto.role;
        if (updateDto.isActive !== undefined && currentUser.isAdmin()) user.isActive = updateDto.isActive;

        const updatedUser = await this.systemUserRepository.save(user);
        this.logger.log(`System user updated successfully: ${updatedUser.username} (ID: ${updatedUser.id})`);

        return updatedUser;
    }

    /**
     * Change system user password
     */
    async changePassword(userId: number, changePasswordDto: SystemUserChangePasswordDto): Promise<void> {
        this.logger.debug(`Changing password for system user: ${userId}`);

        // Validate password confirmation
        if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
            throw new BadRequestException('New password and confirmation do not match');
        }

        // Find user
        const user = await this.systemUserRepository.findOne({
            where: { id: userId }
        });

        if (!user) {
            throw new UnauthorizedException('User not found');
        }

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.passwordHash);
        if (!isCurrentPasswordValid) {
            throw new BadRequestException('Current password is incorrect');
        }

        // Hash new password
        const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
        const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, saltRounds);

        // Update password
        user.passwordHash = hashedNewPassword;
        await this.systemUserRepository.save(user);

        // Revoke all refresh tokens to force re-login
        await this.logoutFromAllDevices(userId);

        this.logger.log(`Password changed successfully for system user: ${user.username} (ID: ${user.id})`);
    }
}
