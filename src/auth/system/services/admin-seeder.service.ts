import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { SystemUser } from '../entities/system-user.entity';

/**
 * Admin Seeder Service
 * Creates default admin user if none exists
 */
@Injectable()
export class AdminSeederService implements OnModuleInit {
    private readonly logger = new Logger(AdminSeederService.name);

    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        private configService: ConfigService,
    ) { }

    async onModuleInit() {
        await this.createDefaultAdmin();
    }

    /**
     * Create default admin user if none exists
     */
    private async createDefaultAdmin(): Promise<void> {
        try {
            // Check if any admin user exists
            const existingAdmin = await this.systemUserRepository.findOne({
                where: { role: 'admin' }
            });

            if (existingAdmin) {
                this.logger.log('Default admin user already exists, skipping seeder');
                this.logger.log('📧 Email: <EMAIL>');
                this.logger.log('👤 Username: admin');
                this.logger.log('🔑 Use existing password or reset if needed');
                return;
            }

            // Create default admin
            const defaultPassword = this.configService.get<string>('DEFAULT_ADMIN_PASSWORD', 'admin123456');
            const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
            const hashedPassword = await bcrypt.hash(defaultPassword, saltRounds);

            const adminUser = this.systemUserRepository.create({
                username: 'admin',
                email: '<EMAIL>',
                passwordHash: hashedPassword,
                fullName: 'System Administrator',
                role: 'admin',
                isActive: true,
            });

            await this.systemUserRepository.save(adminUser);

            this.logger.log('🎉 Default admin user created successfully!');
            this.logger.log('📧 Email: <EMAIL>');
            this.logger.log('👤 Username: admin');
            this.logger.log('🔑 Password: admin123456');
            this.logger.warn('⚠️  Please change the default password after first login!');

        } catch (error) {
            this.logger.error('Failed to create default admin user:', error);
        }
    }

    /**
     * Reset admin password (for development/testing)
     */
    async resetAdminPassword(newPassword: string = 'admin123456'): Promise<void> {
        try {
            const admin = await this.systemUserRepository.findOne({
                where: { username: 'admin' }
            });

            if (!admin) {
                throw new Error('Admin user not found');
            }

            const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
            const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

            admin.passwordHash = hashedPassword;
            await this.systemUserRepository.save(admin);

            this.logger.log('Admin password reset successfully');
        } catch (error) {
            this.logger.error('Failed to reset admin password:', error);
            throw error;
        }
    }
}
