import { Throttle } from '@nestjs/throttler';

/**
 * Rate limiting decorators for different authentication scenarios
 */

/**
 * Strict rate limiting for login attempts
 * 5 attempts per minute per IP
 */
export const LoginRateLimit = () => Throttle({ default: { limit: 5, ttl: 60000 } });

/**
 * Moderate rate limiting for registration
 * 3 attempts per 5 minutes per IP
 */
export const RegisterRateLimit = () => Throttle({ default: { limit: 3, ttl: 300000 } });

/**
 * Lenient rate limiting for token refresh
 * 20 attempts per minute per IP
 */
export const RefreshRateLimit = () => Throttle({ default: { limit: 20, ttl: 60000 } });

/**
 * Very strict rate limiting for admin operations
 * 10 attempts per 5 minutes per IP
 */
export const AdminRateLimit = () => Throttle({ default: { limit: 10, ttl: 300000 } });

/**
 * General API rate limiting
 * 100 requests per minute per IP
 */
export const ApiRateLimit = () => Throttle({ default: { limit: 100, ttl: 60000 } });
