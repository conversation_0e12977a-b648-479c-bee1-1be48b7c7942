import { SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { SystemRole, RegisteredUserTier } from '../types/auth.types';

// Metadata keys
export const IS_PUBLIC_KEY = 'isPublic';
export const ROLES_KEY = 'roles';
export const ADMIN_ONLY_KEY = 'adminOnly';
export const EDITOR_PLUS_KEY = 'editorPlus';
export const TIERS_KEY = 'tiers';
export const EMAIL_VERIFICATION_KEY = 'requireEmailVerification';

/**
 * Public decorator - marks routes as public (no authentication required)
 */
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

/**
 * Roles decorator - specifies required roles for accessing endpoint
 */
export const Roles = (...roles: SystemRole[]) => SetMetadata(ROLES_KEY, roles);

/**
 * CurrentUser decorator - extracts current user from request
 * Supports both SystemUser and RegisteredUser
 */
export const CurrentUser = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): any => {
        const request = ctx.switchToHttp().getRequest();
        return request.user;
    },
);

/**
 * GetCurrentUser decorator - alias for CurrentUser
 */
export const GetCurrentUser = CurrentUser;

/**
 * Tiers decorator - specifies required tiers for registered users
 */
export const RequireTiers = (...tiers: RegisteredUserTier[]) => SetMetadata(TIERS_KEY, tiers);

/**
 * Email verification required decorator
 */
export const RequireEmailVerification = () => SetMetadata(EMAIL_VERIFICATION_KEY, true);

/**
 * Admin Only decorator - shorthand for admin role requirement
 */
export const AdminOnly = () => SetMetadata(ADMIN_ONLY_KEY, true);

/**
 * Editor Plus decorator - allows admin and editor roles
 */
export const EditorPlus = () => SetMetadata(EDITOR_PLUS_KEY, true);

/**
 * Moderator Plus decorator - allows admin and moderator roles
 */
export const ModeratorPlus = () => Roles(SystemRole.ADMIN, SystemRole.MODERATOR);
