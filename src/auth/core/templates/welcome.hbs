<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{appName}}!</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        .btn {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .btn:hover {
            background: #218838;
        }
        .features {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .feature-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{appName}}</div>
            <h1>🎉 Welcome to {{appName}}!</h1>
        </div>

        <p>Hello <strong>{{username}}</strong>,</p>

        <p>Congratulations! Your account has been successfully created and verified. Welcome to the most comprehensive sports data API platform!</p>

        <div style="text-align: center;">
            <a href="{{dashboardUrl}}" class="btn">Go to Dashboard</a>
        </div>

        <div class="features">
            <h3>🚀 What you can do now:</h3>
            
            <div class="feature-item">
                <strong>⚽ Access Live Football Data</strong><br>
                Get real-time fixtures, results, and statistics from major leagues worldwide
            </div>
            
            <div class="feature-item">
                <strong>📊 API Usage Dashboard</strong><br>
                Monitor your API calls, usage limits, and performance metrics
            </div>
            
            <div class="feature-item">
                <strong>🔧 Developer Tools</strong><br>
                Interactive API documentation, code examples, and testing tools
            </div>
            
            <div class="feature-item">
                <strong>📈 Tier Management</strong><br>
                Upgrade to Premium or Enterprise for enhanced features and higher limits
            </div>
        </div>

        <h3>🎯 Your Current Plan:</h3>
        <ul>
            <li><strong>Tier:</strong> Free</li>
            <li><strong>API Calls:</strong> 100 per month</li>
            <li><strong>Access:</strong> Basic football data</li>
            <li><strong>Support:</strong> Community support</li>
        </ul>

        <h3>📚 Getting Started:</h3>
        <ol>
            <li>Visit your dashboard to get your API key</li>
            <li>Check out our interactive API documentation</li>
            <li>Try our code examples and tutorials</li>
            <li>Join our developer community</li>
        </ol>

        <h3>🔗 Useful Links:</h3>
        <ul>
            <li><a href="{{dashboardUrl}}/docs">API Documentation</a></li>
            <li><a href="{{dashboardUrl}}/examples">Code Examples</a></li>
            <li><a href="{{dashboardUrl}}/upgrade">Upgrade Plans</a></li>
            <li><a href="{{dashboardUrl}}/support">Support Center</a></li>
        </ul>

        <p>Ready to build something amazing? We're excited to see what you'll create with our sports data!</p>

        <p>Best regards,<br>
        The {{appName}} Team</p>

        <div class="footer">
            <p>Need help getting started? Contact us at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a></p>
            <p>Follow us for updates and tips on building with sports data</p>
        </div>
    </div>
</body>
</html>
