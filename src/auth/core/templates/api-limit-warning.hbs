<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Usage Warning - {{appName}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #fd7e14;
            margin-bottom: 10px;
        }
        .btn {
            display: inline-block;
            background: #fd7e14;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .btn:hover {
            background: #e8690b;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 5px solid #fd7e14;
        }
        .usage-meter {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-fill {
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .tier-comparison {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .tier-item {
            margin: 10px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{appName}}</div>
            <h1>⚠️ API Usage Warning</h1>
        </div>

        <p>Hello <strong>{{username}}</strong>,</p>

        <div class="warning-box">
            <h3>🚨 You're approaching your API limit!</h3>
            <p>You have used <strong>{{usagePercentage}}%</strong> of your monthly API allowance. To avoid service interruption, consider upgrading your plan or monitoring your usage more closely.</p>
        </div>

        <div class="usage-meter">
            <h3>📊 Current Usage</h3>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {{usagePercentage}}%"></div>
            </div>
            <p><strong>{{usagePercentage}}%</strong> of monthly limit used</p>
        </div>

        {{#if (gte usagePercentage 95)}}
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; color: #721c24;">
            <strong>🚨 Critical Warning:</strong> You've used {{usagePercentage}}% of your API limit. Your service may be interrupted if you exceed 100%. Please upgrade immediately or reduce usage.
        </div>
        {{else}}
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; color: #856404;">
            <strong>⚠️ Warning:</strong> You've used {{usagePercentage}}% of your API limit. Consider upgrading to avoid potential service interruption.
        </div>
        {{/if}}

        <div style="text-align: center;">
            <a href="{{upgradeUrl}}" class="btn">Upgrade Your Plan</a>
        </div>

        <div class="tier-comparison">
            <h3>🚀 Available Upgrade Options:</h3>
            
            <div class="tier-item">
                <strong>💎 Premium Tier</strong><br>
                <strong>10,000 API calls/month</strong> (100x more than Free)<br>
                Priority support, advanced analytics, enhanced features<br>
                <em>Perfect for growing applications</em>
            </div>
            
            <div class="tier-item">
                <strong>🏢 Enterprise Tier</strong><br>
                <strong>Unlimited API calls</strong><br>
                Dedicated support, SLA guarantee, custom solutions<br>
                <em>Ideal for production applications</em>
            </div>
        </div>

        <h3>💡 Tips to Optimize Usage:</h3>
        <ul>
            <li><strong>Cache responses:</strong> Store frequently requested data locally</li>
            <li><strong>Batch requests:</strong> Combine multiple queries when possible</li>
            <li><strong>Filter data:</strong> Request only the data you actually need</li>
            <li><strong>Monitor usage:</strong> Check your dashboard regularly</li>
            <li><strong>Optimize queries:</strong> Use specific parameters to reduce response size</li>
        </ul>

        <h3>📈 Usage Analytics:</h3>
        <p>Track your API usage in real-time and get insights on your application's performance:</p>
        <ul>
            <li><a href="{{dashboardUrl}}/analytics">View Usage Dashboard</a></li>
            <li><a href="{{dashboardUrl}}/docs">API Optimization Guide</a></li>
            <li><a href="{{upgradeUrl}}">Compare Plans & Pricing</a></li>
        </ul>

        <p>Need help optimizing your API usage or have questions about upgrading? Our support team is here to help!</p>

        <p>Best regards,<br>
        The {{appName}} Team</p>

        <div class="footer">
            <p>Need assistance? Contact us at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a></p>
            <p>Manage your usage and upgrade options in your dashboard</p>
        </div>
    </div>
</body>
</html>
