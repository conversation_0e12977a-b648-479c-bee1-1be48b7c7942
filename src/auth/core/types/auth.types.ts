/**
 * Authentication Types
 * Defines types for the dual user system
 */

export enum UserType {
    SYSTEM = 'system',
    REGISTERED = 'registered'
}

export enum SystemRole {
    ADMIN = 'admin',
    EDITOR = 'editor',
    MODERATOR = 'moderator'
}

export enum RegisteredUserTier {
    FREE = 'free',
    PREMIUM = 'premium',
    ENTERPRISE = 'enterprise'
}

/**
 * JWT Payload for System Users
 */
export interface SystemUserJwtPayload {
    sub: number; // user id
    username: string;
    email: string;
    role: SystemRole;
    userType: UserType.SYSTEM;
    iat?: number;
    exp?: number;
}

/**
 * JWT Payload for Registered Users
 */
export interface RegisteredUserJwtPayload {
    sub: number; // user id
    username: string;
    email: string;
    tier: RegisteredUserTier;
    userType: UserType.REGISTERED;
    iat?: number;
    exp?: number;
}

/**
 * JWT Payload for Registered Users
 */
export interface RegisteredUserJwtPayload {
    sub: number; // user id
    username: string;
    email: string;
    tier: RegisteredUserTier;
    userType: UserType.REGISTERED;
    isEmailVerified: boolean;
    iat?: number;
    exp?: number;
}

/**
 * Union type for all JWT payloads
 */
export type JwtPayload = SystemUserJwtPayload | RegisteredUserJwtPayload;

/**
 * Login Request DTOs
 */
export interface SystemUserLoginDto {
    username: string;
    password: string;
}

export interface RegisteredUserLoginDto {
    email: string;
    password: string;
}

/**
 * Registration DTOs
 */
export interface SystemUserCreateDto {
    username: string;
    email: string;
    password: string;
    fullName?: string;
    role: SystemRole;
}

export interface RegisteredUserCreateDto {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
}

/**
 * Authentication Response
 */
export interface AuthResponse {
    accessToken: string;
    refreshToken?: string;
    user: {
        id: number;
        email: string;
        userType: UserType;
        role?: SystemRole;
        accountType?: RegisteredUserTier;
    };
}

/**
 * Permission Definitions
 */
export interface Permission {
    resource: string;
    action: string;
}

export const SYSTEM_PERMISSIONS = {
    // League Management
    LEAGUES_CREATE: { resource: 'leagues', action: 'create' },
    LEAGUES_UPDATE: { resource: 'leagues', action: 'update' },
    LEAGUES_DELETE: { resource: 'leagues', action: 'delete' },

    // Fixture Management
    FIXTURES_CREATE: { resource: 'fixtures', action: 'create' },
    FIXTURES_UPDATE: { resource: 'fixtures', action: 'update' },
    FIXTURES_DELETE: { resource: 'fixtures', action: 'delete' },
    FIXTURES_SYNC: { resource: 'fixtures', action: 'sync' },

    // User Management
    USERS_CREATE: { resource: 'users', action: 'create' },
    USERS_UPDATE: { resource: 'users', action: 'update' },
    USERS_DELETE: { resource: 'users', action: 'delete' },
    USERS_VIEW: { resource: 'users', action: 'view' },

    // System Management
    SYSTEM_CONFIG: { resource: 'system', action: 'config' },
    SYSTEM_LOGS: { resource: 'system', action: 'logs' },
} as const;

export const REGISTERED_USER_PERMISSIONS = {
    // Profile Management
    PROFILE_VIEW: { resource: 'profile', action: 'view' },
    PROFILE_UPDATE: { resource: 'profile', action: 'update' },

    // Content Access
    FIXTURES_VIEW: { resource: 'fixtures', action: 'view' },
    LEAGUES_VIEW: { resource: 'leagues', action: 'view' },
    TEAMS_VIEW: { resource: 'teams', action: 'view' },

    // Premium Features (for premium users)
    PREMIUM_CONTENT: { resource: 'premium', action: 'access' },
} as const;

/**
 * Role Permission Mapping
 */
export const ROLE_PERMISSIONS = {
    [SystemRole.ADMIN]: Object.values(SYSTEM_PERMISSIONS),
    [SystemRole.EDITOR]: [
        SYSTEM_PERMISSIONS.LEAGUES_CREATE,
        SYSTEM_PERMISSIONS.LEAGUES_UPDATE,
        SYSTEM_PERMISSIONS.FIXTURES_CREATE,
        SYSTEM_PERMISSIONS.FIXTURES_UPDATE,
        SYSTEM_PERMISSIONS.FIXTURES_SYNC,
    ],
    [SystemRole.MODERATOR]: [
        SYSTEM_PERMISSIONS.FIXTURES_UPDATE,
        SYSTEM_PERMISSIONS.USERS_VIEW,
    ],
    [RegisteredUserTier.FREE]: [
        REGISTERED_USER_PERMISSIONS.PROFILE_VIEW,
        REGISTERED_USER_PERMISSIONS.PROFILE_UPDATE,
        REGISTERED_USER_PERMISSIONS.FIXTURES_VIEW,
        REGISTERED_USER_PERMISSIONS.LEAGUES_VIEW,
        REGISTERED_USER_PERMISSIONS.TEAMS_VIEW,
    ],
    [RegisteredUserTier.PREMIUM]: [
        ...Object.values(REGISTERED_USER_PERMISSIONS),
    ],
} as const;
