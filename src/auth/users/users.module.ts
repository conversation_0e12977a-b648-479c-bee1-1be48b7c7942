import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Entities
import { RegisteredUser } from './entities/registered-user.entity';
import { UserRefreshToken } from './entities/user-refresh-token.entity';

// Services
import { RegisteredUserService } from './services/registered-user.service';
import { TierManagementService } from './services/tier-management.service';

// Controllers
import { RegisteredUserController } from './controllers/registered-user.controller';
import { AdminController } from './controllers/admin.controller';

// Guards
import { TierAccessGuard } from './guards/tier-access.guard';

// Shared services
import { EmailService } from '../shared/services/email.service';

/**
 * Users Module
 * Handles authentication and management for registered users (public users)
 */
@Module({
    imports: [
        // Database entities
        TypeOrmModule.forFeature([
            RegisteredUser,
            UserRefreshToken,
        ]),

        // JWT configuration
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                secret: configService.get<string>('JWT_SECRET', 'default-secret'),
                signOptions: {
                    expiresIn: configService.get<string>('JWT_ACCESS_EXPIRES_IN', '15m'),
                },
            }),
            inject: [ConfigService],
        }),

        // Passport configuration
        PassportModule.register({ defaultStrategy: 'user-jwt' }),

        // Configuration
        ConfigModule,
    ],

    controllers: [
        RegisteredUserController,
        AdminController,
    ],

    providers: [
        // Services
        RegisteredUserService,
        TierManagementService,
        EmailService,

        // Guards
        TierAccessGuard,
    ],

    exports: [
        // Export services for use in other modules
        RegisteredUserService,
        TierManagementService,

        // Export guards for use in other modules
        TierAccessGuard,

        // Export TypeORM repositories
        TypeOrmModule,
    ],
})
export class UsersModule { }
