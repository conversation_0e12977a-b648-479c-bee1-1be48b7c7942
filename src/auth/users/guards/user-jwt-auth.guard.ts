import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../../core/decorators/auth.decorators';

/**
 * User JWT Authentication Guard
 * Protects routes that require registered user authentication
 */
@Injectable()
export class UserJwtAuthGuard extends AuthGuard('user-jwt') {
    constructor(private reflector: Reflector) {
        super();
    }

    canActivate(context: ExecutionContext) {
        const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        if (isPublic) {
            return true;
        }

        return super.canActivate(context);
    }

    handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
        if (err || !user) {
            throw err || new UnauthorizedException('User authentication required');
        }

        // Ensure this is a registered user
        if (user.userType !== 'registered') {
            throw new UnauthorizedException('Registered user access required');
        }

        return user;
    }
}
