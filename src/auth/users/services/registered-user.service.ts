import { Injectable, ConflictException, NotFoundException, BadRequestException, Logger, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import * as crypto from 'crypto';
import { RegisteredUser } from '../entities/registered-user.entity';
import { UserRefreshToken } from '../entities/user-refresh-token.entity';
import {
    RegisteredUserRegisterDto,
    RegisteredUserLoginDto,
    RegisteredUserAuthResponseDto,
    RegisteredUserProfileDto,
    UpdateProfileDto,
    ChangePasswordDto,
    DeviceInfoDto,
    TokenPairDto
} from '../dto/registered-user.dto';
import { RegisteredUserTier, UserType } from '../../core/types/auth.types';
import { EmailService } from '../../shared/services/email.service';

/**
 * Registered User Service
 * Handles business logic for registered users (end customers)
 */
@Injectable()
export class RegisteredUserService {
    private readonly logger = new Logger(RegisteredUserService.name);

    constructor(
        @InjectRepository(RegisteredUser)
        private readonly registeredUserRepository: Repository<RegisteredUser>,
        @InjectRepository(UserRefreshToken)
        private readonly userRefreshTokenRepository: Repository<UserRefreshToken>,
        private readonly configService: ConfigService,
        private readonly jwtService: JwtService,
        private readonly emailService: EmailService,
    ) { }

    /**
     * Register a new user
     */
    async register(registerDto: RegisteredUserRegisterDto, registrationIp?: string): Promise<RegisteredUser> {
        this.logger.debug(`Registering new user: ${registerDto.username}`);

        // Check if username already exists
        const existingUsername = await this.registeredUserRepository.findOne({
            where: { username: registerDto.username }
        });

        if (existingUsername) {
            throw new ConflictException('Username already exists');
        }

        // Check if email already exists
        const existingEmail = await this.registeredUserRepository.findOne({
            where: { email: registerDto.email }
        });

        if (existingEmail) {
            throw new ConflictException('Email already exists');
        }

        // Hash password
        const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
        const passwordHash = await bcrypt.hash(registerDto.password, saltRounds);

        // Generate email verification token
        const emailVerificationToken = crypto.randomBytes(32).toString('hex');
        const emailVerificationExpiresAt = new Date();
        emailVerificationExpiresAt.setHours(emailVerificationExpiresAt.getHours() + 24); // 24 hours

        // Set API limits based on tier (default: free)
        const apiCallsLimit = this.getApiLimitForTier(RegisteredUserTier.FREE);

        // Create user
        const user = this.registeredUserRepository.create({
            username: registerDto.username,
            email: registerDto.email,
            passwordHash,
            fullName: registerDto.fullName,
            firstName: registerDto.firstName,
            lastName: registerDto.lastName,
            displayName: registerDto.displayName,
            tier: RegisteredUserTier.FREE,
            isActive: true,
            isEmailVerified: false,
            emailVerificationToken,
            emailVerificationExpiresAt,
            apiCallsUsed: 0,
            apiCallsLimit,
            registrationIp,
        });

        const savedUser = await this.registeredUserRepository.save(user);
        this.logger.log(`User registered successfully: ${savedUser.username} (ID: ${savedUser.id})`);

        // Send email verification
        try {
            await this.emailService.sendEmailVerification(
                savedUser.email,
                emailVerificationToken,
                savedUser.username
            );
            this.logger.log(`Email verification sent to: ${savedUser.email}`);
        } catch (error) {
            this.logger.error(`Failed to send verification email to ${savedUser.email}:`, error);
            // Don't fail registration if email fails
        }

        return savedUser;
    }

    /**
     * Login registered user
     */
    async login(loginDto: RegisteredUserLoginDto, deviceInfo?: DeviceInfoDto): Promise<RegisteredUserAuthResponseDto> {
        this.logger.debug(`Login attempt for registered user: ${loginDto.usernameOrEmail}`);

        // Find user by username or email
        const user = await this.findByUsernameOrEmail(loginDto.usernameOrEmail);
        if (!user || !user.isActive) {
            this.logger.warn(`Login failed for: ${loginDto.usernameOrEmail} - User not found or inactive`);
            throw new UnauthorizedException('Invalid credentials');
        }

        // Verify password
        const isPasswordValid = await this.verifyPassword(user, loginDto.password);
        if (!isPasswordValid) {
            this.logger.warn(`Login failed for: ${loginDto.usernameOrEmail} - Invalid password`);
            throw new UnauthorizedException('Invalid credentials');
        }

        // Check if email is verified (optional based on configuration)
        const requireEmailVerification = this.configService.get<boolean>('REQUIRE_EMAIL_VERIFICATION', true);
        if (requireEmailVerification && !user.isEmailVerified) {
            this.logger.warn(`Login failed for: ${loginDto.usernameOrEmail} - Email not verified`);
            throw new UnauthorizedException('Email verification required. Please check your email and verify your account.');
        }

        // Update last login info
        user.lastLoginAt = new Date();
        if (deviceInfo?.ipAddress) {
            user.lastLoginIp = deviceInfo.ipAddress;
        }
        await this.registeredUserRepository.save(user);

        // Generate token pair
        const tokenPair = await this.generateTokenPair(user, deviceInfo);

        this.logger.log(`User ${user.username} logged in successfully`);

        return {
            message: 'Login successful',
            user: this.toProfileDto(user),
            tokens: tokenPair,
        };
    }

    /**
     * Find user by username or email
     */
    async findByUsernameOrEmail(usernameOrEmail: string): Promise<RegisteredUser | null> {
        return this.registeredUserRepository.findOne({
            where: [
                { username: usernameOrEmail },
                { email: usernameOrEmail }
            ]
        });
    }

    /**
     * Find user by ID
     */
    async findById(id: number): Promise<RegisteredUser | null> {
        return this.registeredUserRepository.findOne({
            where: { id }
        });
    }

    /**
     * Find user by email
     */
    async findByEmail(email: string): Promise<RegisteredUser | null> {
        return this.registeredUserRepository.findOne({
            where: { email }
        });
    }

    /**
     * Verify user password
     */
    async verifyPassword(user: RegisteredUser, password: string): Promise<boolean> {
        return bcrypt.compare(password, user.passwordHash);
    }

    /**
     * Update user profile
     */
    async updateProfile(userId: number, updateDto: UpdateProfileDto): Promise<RegisteredUser> {
        const user = await this.findById(userId);
        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Update fields
        if (updateDto.fullName !== undefined) user.fullName = updateDto.fullName;
        if (updateDto.firstName !== undefined) user.firstName = updateDto.firstName;
        if (updateDto.lastName !== undefined) user.lastName = updateDto.lastName;
        if (updateDto.displayName !== undefined) user.displayName = updateDto.displayName;

        const updatedUser = await this.registeredUserRepository.save(user);
        this.logger.debug(`Profile updated for user: ${user.username}`);

        return updatedUser;
    }

    /**
     * Change user password
     */
    async changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<void> {
        const user = await this.findById(userId);
        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Verify current password
        const isCurrentPasswordValid = await this.verifyPassword(user, changePasswordDto.currentPassword);
        if (!isCurrentPasswordValid) {
            throw new BadRequestException('Current password is incorrect');
        }

        // Hash new password
        const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
        const newPasswordHash = await bcrypt.hash(changePasswordDto.newPassword, saltRounds);

        // Update password
        user.passwordHash = newPasswordHash;
        await this.registeredUserRepository.save(user);

        this.logger.log(`Password changed for user: ${user.username}`);
    }

    /**
     * Verify email with token
     */
    async verifyEmail(token: string): Promise<RegisteredUser> {
        const user = await this.registeredUserRepository.findOne({
            where: { emailVerificationToken: token }
        });

        if (!user) {
            throw new BadRequestException('Invalid verification token');
        }

        if (user.isEmailVerified) {
            throw new BadRequestException('Email already verified');
        }

        if (user.isEmailVerificationExpired()) {
            throw new BadRequestException('Verification token has expired');
        }

        // Mark email as verified
        user.isEmailVerified = true;
        user.emailVerifiedAt = new Date();
        user.emailVerificationToken = null;
        user.emailVerificationExpiresAt = null;

        const verifiedUser = await this.registeredUserRepository.save(user);
        this.logger.log(`Email verified for user: ${user.username}`);

        // Send welcome email
        try {
            await this.emailService.sendWelcomeEmail(user.email, user.username);
            this.logger.log(`Welcome email sent to: ${user.email}`);
        } catch (error) {
            this.logger.error(`Failed to send welcome email to ${user.email}:`, error);
            // Don't fail verification if email fails
        }

        return verifiedUser;
    }

    /**
     * Generate new email verification token
     */
    async generateEmailVerificationToken(email: string): Promise<void> {
        const user = await this.findByEmail(email);
        if (!user) {
            throw new NotFoundException('User not found');
        }

        if (user.isEmailVerified) {
            throw new BadRequestException('Email already verified');
        }

        // Generate new token
        const emailVerificationToken = crypto.randomBytes(32).toString('hex');
        const emailVerificationExpiresAt = new Date();
        emailVerificationExpiresAt.setHours(emailVerificationExpiresAt.getHours() + 24); // 24 hours

        user.emailVerificationToken = emailVerificationToken;
        user.emailVerificationExpiresAt = emailVerificationExpiresAt;

        await this.registeredUserRepository.save(user);
        this.logger.debug(`New email verification token generated for: ${email}`);

        // Send new verification email
        try {
            await this.emailService.sendEmailVerification(user.email, emailVerificationToken, user.username);
            this.logger.log(`New verification email sent to: ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send new verification email to ${email}:`, error);
            // Don't fail token generation if email fails
        }
    }

    /**
     * Generate password reset token
     */
    async generatePasswordResetToken(email: string): Promise<void> {
        const user = await this.findByEmail(email);
        if (!user) {
            // Don't reveal if email exists or not
            this.logger.debug(`Password reset requested for non-existent email: ${email}`);
            return;
        }

        // Generate reset token
        const passwordResetToken = crypto.randomBytes(32).toString('hex');
        const passwordResetExpiresAt = new Date();
        passwordResetExpiresAt.setHours(passwordResetExpiresAt.getHours() + 1); // 1 hour

        user.passwordResetToken = passwordResetToken;
        user.passwordResetExpiresAt = passwordResetExpiresAt;

        await this.registeredUserRepository.save(user);
        this.logger.debug(`Password reset token generated for: ${email}`);

        // Send password reset email
        try {
            await this.emailService.sendPasswordReset(user.email, passwordResetToken, user.username);
            this.logger.log(`Password reset email sent to: ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send password reset email to ${email}:`, error);
            // Don't fail token generation if email fails
        }
    }

    /**
     * Reset password with token
     */
    async resetPassword(token: string, newPassword: string): Promise<void> {
        const user = await this.registeredUserRepository.findOne({
            where: { passwordResetToken: token }
        });

        if (!user) {
            throw new BadRequestException('Invalid reset token');
        }

        if (user.isPasswordResetExpired()) {
            throw new BadRequestException('Reset token has expired');
        }

        // Hash new password
        const saltRounds = this.configService.get<number>('BCRYPT_SALT_ROUNDS', 12);
        const passwordHash = await bcrypt.hash(newPassword, saltRounds);

        // Update password and clear reset token
        user.passwordHash = passwordHash;
        user.passwordResetToken = null;
        user.passwordResetExpiresAt = null;

        await this.registeredUserRepository.save(user);
        this.logger.log(`Password reset completed for user: ${user.username}`);
    }

    /**
     * Convert entity to profile DTO
     */
    toProfileDto(user: RegisteredUser): RegisteredUserProfileDto {
        return {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.getFullName(),
            firstName: user.firstName,
            lastName: user.lastName,
            displayName: user.displayName,
            tier: user.tier as RegisteredUserTier,
            isActive: user.isActive,
            isEmailVerified: user.isEmailVerified,
            apiCallsUsed: user.apiCallsUsed,
            apiCallsLimit: user.apiCallsLimit,
            apiCallsRemaining: user.getApiCallsRemaining(),
            hasActiveSubscription: user.hasActiveSubscription(),
            subscriptionEndDate: user.subscriptionEndDate || undefined,
            lastLoginAt: user.lastLoginAt || undefined,
            createdAt: user.createdAt,
        };
    }

    /**
     * Get all users with pagination and filtering (Admin only)
     */
    async getAllUsers(options: {
        page?: number;
        limit?: number;
        tier?: RegisteredUserTier;
        isActive?: boolean;
    }): Promise<{
        data: RegisteredUserProfileDto[];
        meta: {
            totalItems: number;
            totalPages: number;
            currentPage: number;
            limit: number;
        };
    }> {
        const { page = 1, limit = 20, tier, isActive } = options;
        const skip = (page - 1) * limit;

        const queryBuilder = this.registeredUserRepository.createQueryBuilder('user');

        // Apply filters
        if (tier) {
            queryBuilder.andWhere('user.tier = :tier', { tier });
        }

        if (isActive !== undefined) {
            queryBuilder.andWhere('user.isActive = :isActive', { isActive });
        }

        // Get total count
        const totalItems = await queryBuilder.getCount();

        // Apply pagination and get results
        const users = await queryBuilder
            .orderBy('user.createdAt', 'DESC')
            .skip(skip)
            .take(limit)
            .getMany();

        const totalPages = Math.ceil(totalItems / limit);

        return {
            data: users.map(user => this.toProfileDto(user)),
            meta: {
                totalItems,
                totalPages,
                currentPage: page,
                limit,
            },
        };
    }

    /**
     * Generate token pair for registered user
     */
    async generateTokenPair(user: RegisteredUser, deviceInfo?: DeviceInfoDto): Promise<TokenPairDto> {
        // Generate access token
        const accessToken = await this.generateAccessToken(user);

        // Generate refresh token
        const refreshToken = await this.generateRefreshToken(user, deviceInfo);

        return {
            accessToken,
            refreshToken,
            expiresIn: this.configService.get<string>('JWT_ACCESS_EXPIRES_IN', '15m'),
            tokenType: 'Bearer',
        };
    }

    /**
     * Generate access token for registered user
     */
    async generateAccessToken(user: RegisteredUser): Promise<string> {
        const payload = {
            sub: user.id,
            username: user.username,
            email: user.email,
            tier: user.tier,
            userType: UserType.REGISTERED,
            iat: Math.floor(Date.now() / 1000),
        };

        return this.jwtService.signAsync(payload);
    }

    /**
     * Generate refresh token for registered user
     */
    async generateRefreshToken(user: RegisteredUser, deviceInfo?: DeviceInfoDto): Promise<string> {
        // Generate random token
        const token = crypto.randomBytes(32).toString('hex');

        // Calculate expiration
        const expiresAt = new Date();
        const refreshExpiresIn = this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d');

        // Parse expiration (simple parsing for common formats)
        if (refreshExpiresIn.endsWith('d')) {
            const days = parseInt(refreshExpiresIn.slice(0, -1));
            expiresAt.setDate(expiresAt.getDate() + days);
        } else if (refreshExpiresIn.endsWith('h')) {
            const hours = parseInt(refreshExpiresIn.slice(0, -1));
            expiresAt.setHours(expiresAt.getHours() + hours);
        } else {
            // Default to 7 days
            expiresAt.setDate(expiresAt.getDate() + 7);
        }

        // Create refresh token record
        const refreshTokenEntity = this.userRefreshTokenRepository.create({
            token,
            registeredUserId: user.id,
            userType: UserType.REGISTERED,
            expiresAt,
            deviceInfo: deviceInfo?.deviceInfo || 'Unknown device',
            ipAddress: deviceInfo?.ipAddress || 'Unknown IP',
            userAgent: deviceInfo?.userAgent || 'Unknown user agent',
            isRevoked: false,
        });

        await this.userRefreshTokenRepository.save(refreshTokenEntity);

        return token;
    }

    /**
     * Refresh access token using refresh token
     */
    async refreshToken(refreshToken: string): Promise<TokenPairDto> {
        // Find refresh token
        const tokenEntity = await this.userRefreshTokenRepository.findOne({
            where: { token: refreshToken, isRevoked: false },
            relations: ['registeredUser'],
        });

        if (!tokenEntity) {
            throw new UnauthorizedException('Invalid refresh token');
        }

        if (tokenEntity.isExpired()) {
            throw new UnauthorizedException('Refresh token has expired');
        }

        if (tokenEntity.userType !== UserType.REGISTERED) {
            throw new UnauthorizedException('Invalid token type');
        }

        const user = tokenEntity.registeredUser;
        if (!user || !user.isActive) {
            throw new UnauthorizedException('User not found or inactive');
        }

        // Revoke old refresh token
        tokenEntity.isRevoked = true;
        await this.userRefreshTokenRepository.save(tokenEntity);

        // Generate new token pair
        const deviceInfo = {
            ipAddress: tokenEntity.ipAddress,
            userAgent: tokenEntity.userAgent,
            deviceInfo: tokenEntity.deviceInfo,
        };

        return this.generateTokenPair(user, deviceInfo);
    }

    /**
     * Logout user by revoking refresh token
     */
    async logout(refreshToken: string): Promise<void> {
        const tokenEntity = await this.userRefreshTokenRepository.findOne({
            where: { token: refreshToken, isRevoked: false }
        });

        if (tokenEntity) {
            tokenEntity.isRevoked = true;
            await this.userRefreshTokenRepository.save(tokenEntity);
            this.logger.debug(`Refresh token revoked: ${refreshToken.substring(0, 8)}...`);
        }
    }

    /**
     * Logout from all devices by revoking all refresh tokens
     */
    async logoutAll(userId: number): Promise<void> {
        await this.userRefreshTokenRepository.update(
            { registeredUserId: userId, isRevoked: false },
            { isRevoked: true }
        );
        this.logger.log(`All refresh tokens revoked for user ID: ${userId}`);
    }

    /**
     * Get API limit for tier
     */
    private getApiLimitForTier(tier: RegisteredUserTier): number | null {
        switch (tier) {
            case RegisteredUserTier.FREE:
                return this.configService.get<number>('API_LIMIT_FREE', 100);
            case RegisteredUserTier.PREMIUM:
                return this.configService.get<number>('API_LIMIT_PREMIUM', 10000);
            case RegisteredUserTier.ENTERPRISE:
                return null; // Unlimited
            default:
                return 100; // Default to free tier limit
        }
    }
}
