import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RegisteredUser } from '../entities/registered-user.entity';
import { RegisteredUserJwtPayload, UserType } from '../../core/types/auth.types';

/**
 * Registered User JWT Strategy
 * Validates JWT tokens for registered users and injects user context
 */
@Injectable()
export class UserJwtStrategy extends PassportStrategy(Strategy, 'user-jwt') {
    constructor(
        @InjectRepository(RegisteredUser)
        private registeredUserRepository: Repository<RegisteredUser>,
        private configService: ConfigService,
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get<string>('JWT_SECRET', 'default-secret'),
        });
    }

    async validate(payload: RegisteredUserJwtPayload): Promise<RegisteredUser> {
        // Ensure this is a registered user token
        if (payload.userType !== UserType.REGISTERED) {
            throw new UnauthorizedException('Invalid token type for user authentication');
        }

        // Find user in database
        const user = await this.registeredUserRepository.findOne({
            where: { id: payload.sub }
        });

        if (!user) {
            throw new UnauthorizedException('Registered user not found');
        }

        if (!user.isActive) {
            throw new UnauthorizedException('User account is inactive');
        }

        // Update last API call timestamp for usage tracking
        user.lastApiCallAt = new Date();
        await this.registeredUserRepository.save(user);

        // Add userType to user object for guards
        (user as any).userType = 'registered';

        return user;
    }
}
