import { Entity, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * Registered User Entity
 * For end users who register through the application
 * Supports tier-based access control (Free, Premium, Enterprise)
 */
@Entity('registered_users')
export class RegisteredUser {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({ unique: true })
    username: string;

    @Index()
    @Column({ unique: true })
    email: string;

    @Column()
    passwordHash: string;

    @Column({ nullable: true })
    fullName: string;

    @Column({ nullable: true })
    firstName: string;

    @Column({ nullable: true })
    lastName: string;

    @Column({ nullable: true })
    displayName: string;

    // Tier-based access control
    @Column({
        type: 'enum',
        enum: ['free', 'premium', 'enterprise'],
        default: 'free'
    })
    tier: 'free' | 'premium' | 'enterprise';

    @Column({ default: true })
    isActive: boolean;

    @Column({ default: false })
    isEmailVerified: boolean;

    // Email verification
    @Column({ type: 'varchar', nullable: true })
    emailVerificationToken: string | null;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    emailVerificationExpiresAt: Date | null;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    emailVerifiedAt: Date | null;

    // Password reset
    @Column({ type: 'varchar', nullable: true })
    passwordResetToken: string | null;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    passwordResetExpiresAt: Date | null;

    // Subscription tracking
    @Column({ nullable: true, type: 'timestamp with time zone' })
    subscriptionStartDate: Date | null;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    subscriptionEndDate: Date | null;

    @Column({ default: 0 })
    apiCallsUsed: number;

    @Column({ type: 'integer', nullable: true })
    apiCallsLimit: number | null; // null = unlimited

    @Column({ nullable: true, type: 'timestamp with time zone' })
    lastApiCallAt: Date | null;

    // Tracking
    @Column({ nullable: true, type: 'timestamp with time zone' })
    lastLoginAt: Date | null;

    @Column({ type: 'varchar', nullable: true })
    registrationIp: string | null;

    @Column({ type: 'varchar', nullable: true })
    lastLoginIp: string | null;

    @CreateDateColumn({ type: 'timestamp with time zone' })
    createdAt: Date;

    @UpdateDateColumn({ type: 'timestamp with time zone' })
    updatedAt: Date;

    /**
     * Get full name of the user
     */
    getFullName(): string {
        if (this.firstName && this.lastName) {
            return `${this.firstName} ${this.lastName}`;
        }
        return this.displayName || this.fullName || this.email;
    }

    /**
     * Check if user is premium tier
     */
    isPremium(): boolean {
        return this.tier === 'premium';
    }

    /**
     * Check if user is enterprise tier
     */
    isEnterprise(): boolean {
        return this.tier === 'enterprise';
    }

    /**
     * Check if user is free tier
     */
    isFree(): boolean {
        return this.tier === 'free';
    }

    /**
     * Check if user can access premium features
     */
    canAccessPremiumFeatures(): boolean {
        return (this.isPremium() || this.isEnterprise()) && this.isActive && this.isEmailVerified;
    }

    /**
     * Check if user can access enterprise features
     */
    canAccessEnterpriseFeatures(): boolean {
        return this.isEnterprise() && this.isActive && this.isEmailVerified;
    }

    /**
     * Check if email verification is expired
     */
    isEmailVerificationExpired(): boolean {
        if (!this.emailVerificationExpiresAt) return false;
        return new Date() > this.emailVerificationExpiresAt;
    }

    /**
     * Check if password reset token is expired
     */
    isPasswordResetExpired(): boolean {
        if (!this.passwordResetExpiresAt) return false;
        return new Date() > this.passwordResetExpiresAt;
    }

    /**
     * Check if subscription is active
     */
    hasActiveSubscription(): boolean {
        if (this.isFree()) return true; // Free tier is always "active"

        if (!this.subscriptionStartDate || !this.subscriptionEndDate) {
            return false;
        }

        const now = new Date();
        return now >= this.subscriptionStartDate && now <= this.subscriptionEndDate;
    }

    /**
     * Check if user has exceeded API call limit
     */
    hasExceededApiLimit(): boolean {
        if (this.apiCallsLimit === null) return false; // Unlimited
        return this.apiCallsUsed >= this.apiCallsLimit;
    }

    /**
     * Get API calls remaining
     */
    getApiCallsRemaining(): number | null {
        if (this.apiCallsLimit === null) return null; // Unlimited
        return Math.max(0, this.apiCallsLimit - this.apiCallsUsed);
    }

    /**
     * Reset monthly API usage (called by cron job)
     */
    resetMonthlyApiUsage(): void {
        this.apiCallsUsed = 0;
        this.lastApiCallAt = null;
    }

    /**
     * Increment API usage
     */
    incrementApiUsage(): void {
        this.apiCallsUsed += 1;
        this.lastApiCallAt = new Date();
    }
}
