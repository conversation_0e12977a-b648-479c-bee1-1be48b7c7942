import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { RegisteredUser } from './registered-user.entity';
import { UserType } from '../../core/types/auth.types';

/**
 * User Refresh Token Entity
 * Stores refresh tokens for registered users
 */
@Entity('user_refresh_tokens')
export class UserRefreshToken {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({ unique: true })
    token: string;

    @ManyToOne(() => RegisteredUser, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'registeredUserId' })
    registeredUser: RegisteredUser;

    @Index()
    @Column()
    registeredUserId: number;

    @Column({
        type: 'enum',
        enum: UserType,
        default: UserType.REGISTERED
    })
    userType: UserType;

    @Column({ nullable: true })
    deviceInfo: string;

    @Column({ nullable: true })
    ipAddress: string;

    @Column({ nullable: true })
    userAgent: string;

    @Column({ type: 'timestamp with time zone' })
    expiresAt: Date;

    @Column({ default: false })
    isRevoked: boolean;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    revokedAt: Date;

    @CreateDateColumn({ type: 'timestamp with time zone' })
    createdAt: Date;

    /**
     * Check if token is expired
     */
    isExpired(): boolean {
        return new Date() > this.expiresAt;
    }

    /**
     * Check if token is valid (not expired and not revoked)
     */
    isValid(): boolean {
        return !this.isExpired() && !this.isRevoked;
    }

    /**
     * Revoke the token
     */
    revoke(): void {
        this.isRevoked = true;
        this.revokedAt = new Date();
    }
}
