-- Drop existing tables if they exist
DROP TABLE IF EXISTS "player_statistics" CASCADE;
DROP TABLE IF EXISTS "players" CASCADE;

-- Create players table
CREATE TABLE "players" (
    "id" SERIAL PRIMARY KEY,
    "externalId" INTEGER UNIQUE NOT NULL,
    "name" VARCHAR NOT NULL,
    "firstName" VARCHAR,
    "lastName" VARCHAR,
    "age" INTEGER,
    "birthDate" DATE,
    "birthPlace" VARCHAR,
    "birthCountry" VARCHAR,
    "nationality" VARCHAR,
    "height" VARCHAR,
    "weight" VARCHAR,
    "injured" BOOLEAN DEFAULT false,
    "photo" VARCHAR,
    "timestamp" BIGINT DEFAULT EXTRACT(epoch FROM NOW()) * 1000
);

-- Create indexes for players table
CREATE INDEX "IDX_PLAYER_EXTERNAL_ID" ON "players" ("externalId");
CREATE INDEX "IDX_PLAYER_NAME" ON "players" ("name");
CREATE INDEX "IDX_PLAYER_TIMESTAMP" ON "players" ("timestamp");

-- Create player_statistics table
CREATE TABLE "player_statistics" (
    "id" SERIAL PRIMARY KEY,
    "playerId" INTEGER NOT NULL,
    "teamId" INTEGER NOT NULL,
    "teamName" VARCHAR,
    "teamLogo" VARCHAR,
    "leagueId" INTEGER NOT NULL,
    "leagueName" VARCHAR,
    "leagueCountry" VARCHAR,
    "leagueLogo" VARCHAR,
    "season" INTEGER NOT NULL,
    "games" JSONB,
    "substitutes" JSONB,
    "shots" JSONB,
    "goals" JSONB,
    "passes" JSONB,
    "tackles" JSONB,
    "duels" JSONB,
    "dribbles" JSONB,
    "fouls" JSONB,
    "cards" JSONB,
    "penalty" JSONB
);

-- Create indexes for player_statistics table
CREATE INDEX "IDX_PLAYER_STATS_PLAYER" ON "player_statistics" ("playerId");
CREATE INDEX "IDX_PLAYER_STATS_TEAM" ON "player_statistics" ("teamId");
CREATE INDEX "IDX_PLAYER_STATS_LEAGUE" ON "player_statistics" ("leagueId");
CREATE INDEX "IDX_PLAYER_STATS_SEASON" ON "player_statistics" ("season");

-- Create unique constraint
CREATE UNIQUE INDEX "UQ_PLAYER_TEAM_LEAGUE_SEASON" ON "player_statistics" ("playerId", "teamId", "leagueId", "season");

-- Create foreign key
ALTER TABLE "player_statistics" 
ADD CONSTRAINT "FK_PLAYER_STATISTICS_PLAYER" 
FOREIGN KEY ("playerId") REFERENCES "players" ("id") ON DELETE CASCADE;
