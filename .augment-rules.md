# Augment Agent Coding Rules

## 🏗️ **Architecture Principles**

### **1. Service Separation**
- **API Service**: HTTP endpoints, controllers, lightweight operations
- **Worker Service**: Background jobs, cronjobs, heavy processing
- **Shared**: Common utilities, entities, services

### **2. Module Organization**
```
domain/
├── domain-api.module.ts     # API endpoints only
├── domain-worker.module.ts  # Worker functionality only
├── domain.module.ts         # Shared domain logic
├── controllers/             # HTTP controllers (API only)
├── services/               # Business logic (shared)
├── models/                 # Entities (shared)
└── dto/                    # Data transfer objects
```

### **3. Naming Conventions**
- **Files**: kebab-case (user-auth.service.ts)
- **Classes**: PascalCase (UserAuthService)
- **Methods**: camelCase (getUserProfile)
- **Constants**: UPPER_SNAKE_CASE (MAX_RETRY_ATTEMPTS)
- **Entities**: Pascal<PERSON>ase singular (User, not Users)

## 🔧 **Implementation Rules**

### **1. Service Layer**
```typescript
@Injectable()
export class ExampleService {
    private readonly logger = new Logger(ExampleService.name);

    constructor(
        @InjectRepository(Entity)
        private readonly repository: Repository<Entity>,
        private readonly configService: ConfigService,
    ) {}

    // Always use proper error handling
    async method(): Promise<ReturnType> {
        try {
            this.logger.debug('Starting operation');
            // Implementation
            return result;
        } catch (error) {
            this.logger.error(`Operation failed: ${error.message}`);
            throw error;
        }
    }
}
```

### **2. Controller Layer**
```typescript
@Controller('endpoint')
export class ExampleController {
    constructor(private readonly service: ExampleService) {}

    @Get()
    async getItems(): Promise<ResponseDto> {
        try {
            return await this.service.getItems();
        } catch (error) {
            throw new HttpException(
                `Failed to get items: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
```

### **3. Entity Definitions**
```typescript
@Entity('table_name')
export class EntityName {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
```

## 📦 **Module Rules**

### **1. API Modules**
- Only import shared modules and domain modules
- NO SyncModule or BullModule imports
- Controllers and lightweight services only

### **2. Worker Modules**
- Can import SyncModule, BullModule
- Background processing services
- Cronjobs and queue processors

### **3. Shared Modules**
- Utilities, common services
- Database entities
- Configuration services

## 🛡️ **Security & Validation**

### **1. DTOs with Validation**
```typescript
export class CreateUserDto {
    @IsEmail()
    @IsNotEmpty()
    email: string;

    @IsString()
    @MinLength(8)
    password: string;
}
```

### **2. Environment Variables**
- Always use ConfigService
- Never hardcode secrets
- Validate required env vars

### **3. Error Handling**
- Use structured logging
- Proper HTTP status codes
- Don't expose internal errors to clients

## 📊 **Database Rules**

### **1. Repository Pattern**
```typescript
// Use repository methods
await this.repository.find({ where: { active: true } });

// Use query builder for complex queries
await this.repository
    .createQueryBuilder('entity')
    .where('entity.active = :active', { active: true })
    .getMany();
```

### **2. Transactions**
```typescript
await this.dataSource.transaction(async (manager) => {
    await manager.save(entity1);
    await manager.save(entity2);
});
```

### **3. Migrations**
- Always create migrations for schema changes
- Use descriptive migration names
- Test migrations in development first

## 🔄 **API Design**

### **1. RESTful Endpoints**
```
GET    /resource           # List resources
GET    /resource/:id       # Get specific resource
POST   /resource           # Create resource
PATCH  /resource/:id       # Update resource
DELETE /resource/:id       # Delete resource
```

### **2. Response Format**
```typescript
{
    "data": [...],
    "meta": {
        "total": 100,
        "page": 1,
        "limit": 10
    }
}
```

### **3. Query Parameters**
- Use consistent naming (limit, offset, sort, filter)
- Validate all query parameters
- Provide sensible defaults

## 🧪 **Testing Rules**

### **1. Unit Tests**
- Test business logic in services
- Mock external dependencies
- Use descriptive test names

### **2. Integration Tests**
- Test API endpoints
- Use test database
- Clean up after tests

## 📝 **Documentation Rules**

### **1. Code Comments**
- Document complex business logic
- Explain WHY, not WHAT
- Keep comments up to date

### **2. API Documentation**
- Use Swagger/OpenAPI decorators
- Document all endpoints
- Include example requests/responses

### **3. README Updates**
- Document new features
- Update setup instructions
- Maintain changelog

## 🚀 **Performance Rules**

### **1. Database Optimization**
- Use indexes for frequently queried fields
- Implement pagination for large datasets
- Use batch operations for bulk updates

### **2. Caching**
- Cache frequently accessed data
- Use appropriate cache TTL
- Implement cache invalidation

### **3. API Optimization**
- Implement rate limiting
- Use compression
- Optimize query complexity

## 🔧 **Development Workflow**

### **1. Before Implementation**
- Understand requirements clearly
- Check existing patterns in codebase
- Plan module structure

### **2. During Implementation**
- Follow existing code patterns
- Write tests alongside code
- Use proper error handling

### **3. After Implementation**
- Update documentation
- Test thoroughly
- Create summary in LogWorking/
- LogWorking/{feature}/{STT_gio_ngay_thang_nam_tenchucnang.md}
- Document performance metrics và optimization results
- Update Swagger API documentation với examples

## 📋 **Checklist for New Features**

- [ ] Follow clear module separation (API / Worker / Shared)
- [ ] Do NOT directly access logic from other modules
- [ ] Communicate via interfaces or event-driven patterns
- [ ] Implement proper error handling
- [ ] Add logging where appropriate
- [ ] Create/update DTOs with validation
- [ ] Write unit tests
- [ ] Update API documentation
- [ ] Test endpoints manually
- [ ] Create documentation in LogWorking/
- [ ] Update README if needed
- [ ] Document lessons learned và best practices
- [ ] Include performance benchmarks if applicable

## 🎯 **Priority Order**

1. **Functionality**: Make it work
2. **Security**: Make it secure
3. **Performance**: Make it fast
4. **Maintainability**: Make it clean
5. **Documentation**: Make it clear

Remember: Always ask for clarification if requirements are unclear!

## 🔐 **Authentication System Specific Rules**

### **1. Auth Module Structure**
```
src/auth/
├── auth-api.module.ts       # API endpoints (login, register)
├── auth.module.ts           # Shared auth logic
├── controllers/
│   └── auth.controller.ts   # Authentication endpoints
├── services/
│   ├── auth.service.ts      # Authentication business logic
│   └── user.service.ts      # User management
├── entities/
│   ├── user.entity.ts       # User entity
│   └── role.entity.ts       # Role entity (if needed)
├── dto/
│   ├── login.dto.ts
│   ├── register.dto.ts
│   └── user-response.dto.ts
├── guards/
│   ├── jwt-auth.guard.ts
│   └── roles.guard.ts
└── strategies/
    └── jwt.strategy.ts
```

### **2. Security Requirements**
- Always hash passwords with bcrypt
- Use JWT with proper expiration
- Implement role-based access control
- Validate all input data
- Never expose sensitive data in responses

### **3. Integration Rules**
- Add auth guards to existing football endpoints
- Maintain backward compatibility
- Use dependency injection properly
- Follow existing error handling patterns

## 👨‍💻 **Developer's Custom Rules**

### **[Add Your Programming Principles Here]**

**Example sections you can add:**
- Preferred coding patterns
- Specific naming conventions
- Error handling preferences
- Testing requirements
- Documentation standards
- Performance considerations
- Security requirements

**Instructions for developer:**
1. Add your specific rules below this section
2. Be as detailed as you want
3. Include examples if helpful
4. Update as project evolves

### **Custom Rules Section:**
```

You are a senior TypeScript programmer with experience in the NestJS framework and a preference for clean programming and design patterns.

Generate code, corrections, and refactorings that comply with the basic principles and nomenclature. Using vietnamese in your responses for better communication ( comment/explaintion), just keep the code in English.

## TypeScript General Guidelines

### Basic Principles

- Use English for all code and documentation.
- Always declare the type of each variable and function (parameters and return value).
  - Avoid using any.
  - Create necessary types.
- Use JSDoc to document public classes and methods.
- Don't leave blank lines within a function.
- One export per file.

### Nomenclature

- Use PascalCase for classes.
- Use camelCase for variables, functions, and methods.
- Use kebab-case for file and directory names.
- Use UPPERCASE for environment variables.
  - Avoid magic numbers and define constants.
- Start each function with a verb.
- Use verbs for boolean variables. Example: isLoading, hasError, canDelete, etc.
- Use complete words instead of abbreviations and correct spelling.
  - Except for standard abbreviations like API, URL, etc.
  - Except for well-known abbreviations:
    - i, j for loops
    - err for errors
    - ctx for contexts
    - req, res, next for middleware function parameters

### Functions

- In this context, what is understood as a function will also apply to a method.
- Write short functions with a single purpose. Less than 20 instructions.
- Name functions with a verb and something else.
  - If it returns a boolean, use isX or hasX, canX, etc.
  - If it doesn't return anything, use executeX or saveX, etc.
- Avoid nesting blocks by:
  - Early checks and returns.
  - Extraction to utility functions.
- Use higher-order functions (map, filter, reduce, etc.) to avoid function nesting.
  - Use arrow functions for simple functions (less than 3 instructions).
  - Use named functions for non-simple functions.
- Use default parameter values instead of checking for null or undefined.
- Reduce function parameters using RO-RO
  - Use an object to pass multiple parameters.
  - Use an object to return results.
  - Declare necessary types for input arguments and output.
- Use a single level of abstraction.

### Data

- Don't abuse primitive types and encapsulate data in composite types.
- Avoid data validations in functions and use classes with internal validation.
- Prefer immutability for data.
  - Use readonly for data that doesn't change.
  - Use as const for literals that don't change.

### Classes

- Follow SOLID principles.
- Prefer composition over inheritance.
- Declare interfaces to define contracts.
- Write small classes with a single purpose.
  - Less than 200 instructions.
  - Less than 10 public methods.
  - Less than 10 properties.

### Exceptions

- Use exceptions to handle errors you don't expect.
- If you catch an exception, it should be to:
  - Fix an expected problem.
  - Add context.
  - Otherwise, use a global handler.

### Testing

- Follow the Arrange-Act-Assert convention for tests.
- Name test variables clearly.
  - Follow the convention: inputX, mockX, actualX, expectedX, etc.
- Write unit tests for each public function.
  - Use test doubles to simulate dependencies.
    - Except for third-party dependencies that are not expensive to execute.
- Write acceptance tests for each module.
  - Follow the Given-When-Then convention.


  ## Specific to NestJS

  ### Basic Principles

  - Use modular architecture.
  - Encapsulate the API in modules.
    - One module per main domain/route.
    - One controller for its route.
      - And other controllers for secondary routes.
    - A models folder with data types.
      - DTOs validated with class-validator for inputs.
      - Declare simple types for outputs.
    - A services module with business logic and persistence.
      - Entities with MikroORM for data persistence.
      - One service per entity.

  - Common Module: Create a common module (e.g., @app/common) for shared, reusable code across the application.
    - This module should include:
      - Configs: Global configuration settings.
      - Decorators: Custom decorators for reusability.
      - DTOs: Common data transfer objects.
      - Guards: Guards for role-based or permission-based access control.
      - Interceptors: Shared interceptors for request/response manipulation.
      - Notifications: Modules for handling app-wide notifications.
      - Services: Services that are reusable across modules.
      - Types: Common TypeScript types or interfaces.
      - Utils: Helper functions and utilities.
      - Validators: Custom validators for consistent input validation.

  - Core module functionalities:
    - Global filters for exception handling.
    - Global middlewares for request management.
    - Guards for permission management.
    - Interceptors for request processing.

### Testing

- Use the standard Jest framework for testing.
- Write tests for each controller and service.
- Write end to end tests for each api module.
- Add a admin/test method to each controller as a smoke test.

```


## 🔗 Module Communication Rules

- ❌ Do NOT directly call business logic across modules (except shared services).
- ✅ Modules should communicate via:
  - Application Events (pub/sub, event emitter).
  - Message Queues (e.g., RabbitMQ, Redis Pub/Sub).
  - Internal REST/gRPC APIs (in case of microservices).
- Each module should expose a public interface (class or contract interface).
- Ensure **Single Responsibility & Separation of Concerns** principles.


## 🧩 Module Design Checklist

Before creating a new module, ensure the following:
- [ ] Clearly define the domain logic (should not mix logic from other domains).
- [ ] Should work independently from other modules.
- [ ] Communicate with others only through interfaces or events.
- [ ] Can be tested and deployed separately if needed.
- [ ] Has a clear structure: `domain-x-api.module.ts`, `domain-x-worker.module.ts`, `domain-x.module.ts`.


## 📦 Example: "Contract" Module Structure

```
src/contracts/
├── contracts-api.module.ts
├── contracts-worker.module.ts
├── contracts.module.ts
├── controllers/
│   └── contract.controller.ts
├── services/
│   └── contract.service.ts
├── events/
│   └── contract.events.ts
├── listeners/
│   └── contract-created.listener.ts
├── dto/
│   ├── create-contract.dto.ts
│   └── update-contract.dto.ts
├── entities/
│   └── contract.entity.ts
└── interfaces/
    └── contract-service.interface.ts
```


## 📘 Notes for New Developers

> Each feature must be encapsulated in an independent module.
> If you need to interact with another module, always use **interfaces, events, or APIs**.
> Do NOT directly call services or logic from unrelated modules, except shared modules.
