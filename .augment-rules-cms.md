# APISportsGame CMS - Augment Agent Rules

## 📖 **PRIMARY REFERENCE DOCUMENT**

**CRITICAL**: Always read and reference `CMS_DEVELOPMENT_GUIDE.md` before any development work.

This document contains:
- Complete API specifications
- Data models and TypeScript interfaces  
- Authentication flows
- Business logic rules
- Current development status

## 🏗️ **Architecture Principles**

### **1. NextJS 14 App Router Structure**
```
app/
├── (auth)/          # Authentication pages
├── (dashboard)/     # Protected dashboard pages
├── api/            # API proxy routes
└── globals.css     # Global styles
```

### **2. Component Organization**
```
components/
├── ui/             # Reusable UI components
├── forms/          # Form components with validation
├── tables/         # Data tables with pagination
├── charts/         # Analytics and visualization
└── layouts/        # Layout components
```

### **3. State Management**
- Use Zustand for global state
- Use TanStack Query for server state
- Use React Hook Form for form state

## 🔧 **Development Rules**

### **1. TypeScript Requirements**
- ALWAYS use TypeScript interfaces from CMS_DEVELOPMENT_GUIDE.md
- Copy interfaces exactly as documented
- Use strict type checking
- No `any` types allowed

### **2. API Integration**
```typescript
// Base API client setup
const API_BASE_URL = 'http://localhost:3000';

// Always use documented endpoints
const endpoints = {
  auth: {
    login: '/auth/login',
    profile: '/auth/profile',
    refresh: '/auth/refresh'
  },
  users: {
    register: '/users/register',
    profile: '/users/profile',
    apiUsage: '/users/api-usage'
  },
  // ... as documented in CMS_DEVELOPMENT_GUIDE.md
};
```

### **3. Authentication Flow**
- Implement dual authentication (SystemUser + RegisteredUser)
- Use JWT tokens as documented
- Handle token refresh automatically
- Implement role-based access control

### **4. Error Handling**
```typescript
// Use documented error format
interface ApiError {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  path: string;
}
```

## 🎨 **UI/UX Guidelines**

### **1. Design System**
- Use Ant Design components
- Implement consistent spacing (Tailwind)
- Support dark/light mode
- Responsive design (mobile-first)

### **2. Component Patterns**
```typescript
// Page component structure
export default function PageName() {
  // 1. Hooks and state
  // 2. API calls with React Query
  // 3. Event handlers
  // 4. Render JSX
}

// Form component structure
export function FormName() {
  // 1. Form schema with Zod
  // 2. React Hook Form setup
  // 3. Submit handler
  // 4. Form JSX with validation
}
```

### **3. Data Tables**
- Use Ant Design Table
- Implement pagination as documented
- Add search and filtering
- Support bulk operations

## 📊 **Feature Implementation Priority**

### **Phase 1: Foundation**
1. Authentication system
2. Basic layout and navigation
3. API client setup
4. Error handling

### **Phase 2: Core Features**
1. User management (SystemUser + RegisteredUser)
2. League management
3. Team management
4. Basic dashboard

### **Phase 3: Advanced Features**
1. Fixture management
2. Sync operations monitoring
3. Analytics dashboard
4. API usage tracking

### **Phase 4: Polish**
1. Performance optimization
2. Advanced filtering
3. Export functionality
4. Real-time updates

## 🔒 **Security Rules**

### **1. Authentication**
- Store JWT in httpOnly cookies (NextAuth.js)
- Implement automatic token refresh
- Handle authentication errors gracefully
- Redirect to login on 401 errors

### **2. Authorization**
- Check user roles before rendering components
- Implement route protection
- Respect tier-based permissions
- Hide features based on user type

### **3. API Security**
- Use HTTPS in production
- Implement CSRF protection
- Validate all inputs
- Sanitize user data

## 🚀 **Performance Rules**

### **1. NextJS Optimizations**
- Use Next.js Image component for logos
- Implement proper caching strategies
- Use dynamic imports for code splitting
- Optimize bundle size

### **2. React Query Configuration**
```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});
```

### **3. State Management**
- Use React Query for server state
- Use Zustand for client state
- Avoid unnecessary re-renders
- Implement proper memoization

## 📝 **Code Quality Rules**

### **1. File Naming**
- Use kebab-case for files and folders
- Use PascalCase for components
- Use camelCase for functions and variables

### **2. Import Organization**
```typescript
// 1. React imports
import React from 'react';

// 2. Third-party imports
import { Button } from 'antd';

// 3. Internal imports
import { ApiClient } from '@/lib/api';

// 4. Relative imports
import './styles.css';
```

### **3. Component Structure**
- One component per file
- Export default for main component
- Export named for utility functions
- Use TypeScript interfaces for props

## 🔄 **Development Workflow**

### **1. Before Starting Any Feature**
1. Read CMS_DEVELOPMENT_GUIDE.md
2. Check Development Status section
3. Understand API endpoints needed
4. Plan component structure

### **2. During Development**
1. Follow documented API specifications
2. Use exact TypeScript interfaces
3. Test against real API endpoints
4. Handle loading and error states

### **3. After Implementation**
1. Test all user flows
2. Verify responsive design
3. Check accessibility
4. Update documentation if needed

## 🧪 **Testing Requirements**

### **1. Component Testing**
- Test user interactions
- Test error states
- Test loading states
- Test responsive behavior

### **2. API Integration Testing**
- Test with real API endpoints
- Test authentication flows
- Test error handling
- Test data validation

## 📚 **Documentation Rules**

### **1. Component Documentation**
- Document props with TypeScript
- Add JSDoc comments for complex logic
- Include usage examples
- Document accessibility features

### **2. API Integration Documentation**
- Document API client usage
- Include error handling examples
- Document authentication setup
- Keep endpoint documentation updated

---

**Remember**: CMS_DEVELOPMENT_GUIDE.md is the single source of truth. Always reference it before implementing any feature!
