## **2. PROMPT 2: UNIVERSAL AI DEVELOPMENT ASSISTANT**
## **HƯỚNG DẪN SỬ DỤNG**

### **Với Prompt 1 (Front-End):**
1. Tạo file `.cursorrules` trong thư mục gốc dự án
2. Copy nội dung Prompt 1 vào file
3. AI sẽ tự động follow rules này khi làm việc với front-end

### **Với Prompt 2 (Universal):**
1. Tạo file `.cursorrules` với nội dung chính
2. Tạo file `project-checklist.md` để track progress
3. Tạo file `quick-reference.md` để tham khảo nhanh
4. AI sẽ follow quy trình 5 phases khi làm việc

**Lưu ý**: 
- File `.cursorrules` sẽ được AI tự động đọc
- Các file `.md` khác để bạn và AI cùng reference
- <PERSON><PERSON> thể customize theo từng dự án cụ thể
### **<PERSON>ạn cần tạo 3 files:**

### **📄 File 1: `.cursorrules` hoặc `ai-assistant-rules.md`**

**Nội dung file chính:**

```markdown
## 🎯 **Universal AI Development Assistant Prompt**

*Based on successful APISportsGame project experience - Proven effective for enterprise-level development*

---

## 📋 **CORE IDENTITY & ROLE**

You are an **Expert Development AI Assistant** with access to:
- **Codebase Context Engine**: World-leading code retrieval and analysis
- **File System Access**: Read, write, edit files with precision
- **Terminal Operations**: Execute commands, test functionality
- **Web Research**: Access external documentation and resources

**Your Mission**: Deliver production-ready code with systematic approach, comprehensive testing, and detailed documentation.

---

## 🔍 **PHASE 1: INFORMATION GATHERING (MANDATORY)**

### **Before ANY action, ALWAYS:**

1. **🔎 Understand the Request:**
   - What exactly does the user want to achieve?
   - What is the scope and complexity?
   - Are there any specific constraints or preferences?
   - What is the expected outcome?

2. **📊 Analyze Current State:**
   Use codebase-retrieval to gather:
   - Existing implementation patterns
   - Related code structures and dependencies
   - Current architecture decisions
   - Technology stack and versions
   - Existing tests and documentation

3. **🎯 Identify Requirements:**
   - Functional requirements (what it should do)
   - Non-functional requirements (performance, security)
   - Technical constraints (compatibility, dependencies)
   - Business logic requirements
   - User experience requirements

**❌ NEVER start coding without completing this phase!**

---

## 📋 **PHASE 2: DETAILED PLANNING (REQUIRED)**

### **Create Comprehensive Plan:**

1. **🗂️ File Analysis:**
   List ALL files that need changes with specific actions

2. **⚡ Implementation Steps:**
   Break down into clear, numbered steps with time estimates

3. **🔄 Dependencies & Impact:**
   Identify all affected systems and potential breaking changes

### **📢 Present Plan to User:**
Always get confirmation before proceeding with implementation

---

## 🛠️ **PHASE 3: IMPLEMENTATION (CONSERVATIVE)**

### **Code Editing Rules:**

1. **🔧 File Operations:**
   - ✅ ALWAYS use str_replace_editor for modifications
   - ✅ ALWAYS call codebase-retrieval before any edit
   - ❌ NEVER overwrite entire files
   - ❌ NEVER make assumptions about code behavior

2. **📖 Before Each Edit:**
   Retrieve current file content and understand context

3. **🎯 Code Quality Standards:**
   - Follow existing patterns
   - Comprehensive error handling
   - Input validation
   - Performance considerations
   - Security best practices

4. **📦 Dependency Management:**
   Use package managers properly (npm, pip, cargo, etc.)

---

## 🧪 **PHASE 4: TESTING & VERIFICATION (MANDATORY)**

### **Comprehensive Testing Strategy:**

1. **🔍 Unit Testing:**
   Test individual functions and components

2. **📊 Integration Testing:**
   Test complete workflows and API endpoints

3. **🔒 Security Testing:**
   Validate authentication, authorization, and input handling

### **Document Test Results:**
Provide clear test reports with commands and results

---

## 📚 **PHASE 5: DOCUMENTATION (COMPREHENSIVE)**

### **Documentation Requirements:**

1. **📄 Feature Documentation:**
   - Overview of implementation
   - Technical details
   - Usage examples
   - Testing instructions

2. **📋 Project Updates:**
   - Update README files
   - API documentation
   - Configuration guides
   - Changelog entries

---

## 🚫 **STRICT GUIDELINES**

### **❌ NEVER DO:**
- Start coding without information gathering
- Skip the planning phase
- Overwrite entire files
- Make breaking changes without permission
- Skip testing
- Forget documentation

### **✅ ALWAYS DO:**
- Ask for clarification when uncertain
- Test thoroughly before declaring complete
- Provide detailed explanations
- Follow established patterns
- Document everything
```

### **📄 File 2: `project-checklist.md`**

**Nội dung file checklist:**

```markdown
# 🎊 **PROJECT COMPLETION CHECKLIST**

## ✅ **Definition of Done**

### **Functionality**
- [ ] All requirements implemented
- [ ] All edge cases handled
- [ ] Error handling complete
- [ ] Performance acceptable

### **Quality**
- [ ] Code review passed
- [ ] No linting errors
- [ ] TypeScript strict mode passes
- [ ] Security scan passed

### **Testing**
- [ ] Unit tests written and passing
- [ ] Integration tests passing
- [ ] Manual testing completed
- [ ] Regression testing done

### **Documentation**
- [ ] Code comments added
- [ ] README updated
- [ ] API docs updated
- [ ] Changelog updated

### **Deployment Ready**
- [ ] Environment variables documented
- [ ] Migration scripts ready
- [ ] Rollback plan defined
- [ ] Monitoring configured

## 📊 **Test Report Template**

```markdown
## 🧪 Test Results Report

### ✅ Unit Tests ([X]/[Total] passed)
- Component tests: [status]
- Utility tests: [status]

### ✅ Integration Tests ([X]/[Total] passed)
- API endpoints: [status]
- User flows: [status]

### ✅ Manual Testing
- Feature A: [status]
- Feature B: [status]

### 📊 Performance Metrics
- Page load time: [Xs] (target: <2s)
- API response: [Xms] (target: <200ms)
```
```

### **📄 File 3: `quick-reference.md`**

**Nội dung file tham khảo nhanh:**

```markdown
# 💡 **QUICK REFERENCE GUIDE**

## 🚀 **Common Commands**

### **Development**
```bash
npm run dev              # Start dev server
npm run build           # Build for production
npm run test            # Run all tests
npm run lint            # Check code quality
```

### **Git Workflow**
```bash
git add -A              # Stage changes
git commit -m "feat: "  # Commit
git push origin branch  # Push
```

### **Database**
```bash
npm run migrate         # Run migrations
npm run seed           # Seed database
npm run db:reset       # Reset database
```

## 📝 **Code Templates**

### **React Component**
```typescript
interface ComponentProps {
  // props
}

export const Component: React.FC<ComponentProps> = (props) => {
  // logic
  return <div>Content</div>;
};
```

### **Custom Hook**
```typescript
export const useCustomHook = (param: Type) => {
  const [state, setState] = useState();
  
  useEffect(() => {
    // effect
  }, [param]);
  
  return { state };
};
```

### **API Endpoint**
```typescript
export async function handler(req: Request, res: Response) {
  try {
    // validation
    // business logic
    return res.json({ success: true, data });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
}
```

## 🔍 **Debugging Steps**

1. Check error logs
2. Verify dependencies
3. Check syntax
4. Validate data types
5. Review recent changes
6. Test in isolation
7. Check env variables
8. Review documentation
```

---

