#!/usr/bin/env node

/**
 * Simple HTTP server to serve CMS_DEVELOPMENT_GUIDE.md
 * Allows AI to fetch latest content via HTTP
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8080;
const FILE_PATH = path.join(__dirname, 'CMS_DEVELOPMENT_GUIDE.md');

const server = http.createServer((req, res) => {
    // Enable CORS for all origins
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    if (req.url === '/cms-guide' && req.method === 'GET') {
        try {
            // Read file content in real-time
            const content = fs.readFileSync(FILE_PATH, 'utf8');
            const lastModified = fs.statSync(FILE_PATH).mtime;
            
            res.setHeader('Content-Type', 'text/plain; charset=utf-8');
            res.setHeader('Last-Modified', lastModified.toUTCString());
            
            // Add metadata header
            const metadata = {
                lastUpdated: lastModified.toISOString(),
                fileSize: content.length,
                version: extractVersion(content)
            };
            
            res.setHeader('X-File-Metadata', JSON.stringify(metadata));
            res.writeHead(200);
            res.end(content);
            
            console.log(`[${new Date().toISOString()}] CMS Guide served - Size: ${content.length} bytes`);
        } catch (error) {
            console.error('Error reading file:', error);
            res.writeHead(500);
            res.end('Error reading CMS Development Guide');
        }
    } else if (req.url === '/cms-guide/metadata' && req.method === 'GET') {
        try {
            const stats = fs.statSync(FILE_PATH);
            const content = fs.readFileSync(FILE_PATH, 'utf8');
            
            const metadata = {
                lastModified: stats.mtime.toISOString(),
                size: stats.size,
                version: extractVersion(content),
                status: 'available'
            };
            
            res.setHeader('Content-Type', 'application/json');
            res.writeHead(200);
            res.end(JSON.stringify(metadata, null, 2));
        } catch (error) {
            res.writeHead(500);
            res.end(JSON.stringify({ error: 'File not accessible' }));
        }
    } else if (req.url === '/health' && req.method === 'GET') {
        res.setHeader('Content-Type', 'application/json');
        res.writeHead(200);
        res.end(JSON.stringify({ 
            status: 'healthy', 
            timestamp: new Date().toISOString(),
            service: 'CMS Development Guide Server'
        }));
    } else {
        res.writeHead(404);
        res.end('Not Found\n\nAvailable endpoints:\n- GET /cms-guide\n- GET /cms-guide/metadata\n- GET /health');
    }
});

function extractVersion(content) {
    const versionMatch = content.match(/\*\*Version:\*\* (.+)/);
    return versionMatch ? versionMatch[1] : '1.0.0';
}

server.listen(PORT, () => {
    console.log(`🚀 CMS Development Guide Server running on http://localhost:${PORT}`);
    console.log(`📖 Access guide at: http://localhost:${PORT}/cms-guide`);
    console.log(`📊 Metadata at: http://localhost:${PORT}/cms-guide/metadata`);
    console.log(`❤️  Health check: http://localhost:${PORT}/health`);
    console.log(`📁 Serving file: ${FILE_PATH}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down CMS Guide Server...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

// Watch file for changes (optional logging)
if (fs.existsSync(FILE_PATH)) {
    fs.watchFile(FILE_PATH, (curr, prev) => {
        console.log(`📝 CMS_DEVELOPMENT_GUIDE.md updated at ${curr.mtime.toISOString()}`);
    });
}
