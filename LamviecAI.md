nguyên tắt làm việc AI.
1. <PERSON><PERSON><PERSON><PERSON> kế xong cấu trúc thư mục
2. <PERSON><PERSON><PERSON> truc DB cơ bản dự án
3. Luôn hỏi AI cần file nào trước khi code 1 chức năng.
4. <PERSON><PERSON> cấp files và nội dung: "Các file đang dùng"



Refactor cấu trúc dự án:
 tạo một checklist chi tiết và theo dõi từng bước để đảm bảo không quên việc gì. Hãy để tôi bắt đầu refactor một cách có hệ thống.

###
FE: components phải được phân chit nhỏ khi phát triển.
Tránh xung đột với các chức năng khác, hãy chia nhỏ các components nha.

Làm vấn đề gì thì nêu vai trò như chuyên gia lĩnh vực đó. VD: Nế<PERSON> bạn chuyên gia UI/UX
###
Bắt đầu:
Tôi cần bạn kiểm tra mã nguồn của tôi, project_structure.txt là cấu trúc thư mực dự án. 
Cùng một source code, tôi đang chạy 2 service
- Servive  APISportsGame( main.ts): dùng để handle các HTTP request.
- service  AutoUpdateSportsGame ( worker.ts): tự động cập nhật data.
Kiềm tra cách import cảu tôi có tối ưu hay không?


Tạo folder tên: LogWorking, để lưu các tóm tắt nhừng gì đã hoàn thành, theo format
STT_gio_phut_ngay_thang_nam_tenchucnang.md và tạo doucment nếu có.
DEPLOYMENT_GUIDE.md
Read.me nội dung LogWorking - Tóm tắt công việc đã hoàn thành
Têu cầu AI tạo file rule để báo theo nguyên tắt code ( .augment-rules.md)

Tôi cần bạn luôn nhớ cấu trúc dự án trong lúc phát triển. Tôi đề xuất:
1. LogWorking/README.md: Complete project overview
2. Luôn cập nhật các file mỗi khi hoàn thành một chức năng/module, để dễ theo dõi và review lại.

VD promt:
Đây là thư mục root dự án FE CMS  ( tên APISportsGamev2-FECMS), sử dụng api từ service tên APISportsGame. Đảm bảo AI luôn nhớ cấu trúc dự án, quá trình thay đổi và cải tiến, tôi đề xuất:
- Tạo file Rule cho phát triển code tên:  .augment-rules.md .
- Tạo thư mục LogWorking lưu tóm tắt từng chức năng đã hoàn thành, theo format 
số thứ tự_gio_phut_ngay_thang_nam_tenchucnang.md, các file này giúp AI review và nhớ những gì mình làm, tôi tiện theo dõi. Tạo doucment nếu có: DEPLOYMENT_GUIDE.md, README.md ghi Complete project overview.
- Theo dõi cấu trúc mã nguồn tại : project_structure.txt

Project này là CMS ( tập trung các tính năng quản lý thể thao): Quản lý user system, quản lý user register, quản lý giải đấu, quản lý trận đấu), page login. Nếu bạn đã hiểu, chúng ta bắt đầu phân tích và thiết kế cấu trúc dự án  chi tiết theo module hóa. Bạn có câu hỏi nào cho tôi? 

Sau khi trả lời câu hỏi, yêu cầu AI lập kế hoạch ( hoặc lặp kế hoạch sau khi đọc document API swagger)



promt moi
{
      # AI Project Startup Prompt Template


---

**I'm starting a new [PROJECT_TYPE] project and need your help as my development partner. Here's the complete context:**

## 📋 **Project Overview**

**Project Name:** [PROJECT_NAME]
**Project Type:** [Web Application/Mobile App/API/CMS/E-commerce/etc.]
**Technology Stack:** [NestJS/React/Vue/Angular/Next.js/etc.]
**Database:** [PostgreSQL/MongoDB/MySQL/etc.]
**Deployment:** [Docker/AWS/Vercel/etc.]

**Project Description:**
[Describe your project in 2-3 sentences - what it does, who it's for, main features]

## 🎯 **Project Goals & Requirements**

**Primary Goals:**
- [Goal 1: e.g., Create a sports data API]
- [Goal 2: e.g., Build admin dashboard]
- [Goal 3: e.g., Implement user authentication]

**Key Features:**
- [Feature 1: e.g., User registration/login]
- [Feature 2: e.g., Data synchronization]
- [Feature 3: e.g., Real-time updates]
- [Feature 4: e.g., Admin panel]

**Target Users:**
- [User Type 1: e.g., System administrators]
- [User Type 2: e.g., Regular users]
- [User Type 3: e.g., API consumers]

## 🏗️ **Technical Requirements**

**Architecture Preferences:**
- [e.g., Microservices/Monolithic/Serverless]
- [e.g., RESTful API/GraphQL]
- [e.g., MVC pattern/Clean Architecture]

**Performance Requirements:**
- [e.g., Handle 1000+ concurrent users]
- [e.g., Response time < 200ms]
- [e.g., 99.9% uptime]

**Security Requirements:**
- [e.g., JWT authentication]
- [e.g., Role-based access control]
- [e.g., Data encryption]

**Integration Requirements:**
- [e.g., Third-party APIs]
- [e.g., Payment gateways]
- [e.g., External services]

## 📁 **Development Preferences**

**Code Quality Standards:**
- TypeScript for type safety
- ESLint + Prettier for code formatting
- Comprehensive error handling
- Input validation and sanitization
- Detailed logging and monitoring

**Documentation Requirements:**
- API documentation (Swagger/OpenAPI)
- Code comments for complex logic
- README with setup instructions
- Architecture documentation

**Testing Strategy:**
- Unit tests for business logic
- Integration tests for APIs
- End-to-end testing for critical flows

## 🔧 **Development Workflow**

**Project Organization:**
- Create detailed implementation plans before coding
- Break down tasks into manageable phases
- Document each completed phase
- Maintain project progress tracking

**File Management:**
- Use descriptive file and folder names
- Organize code by feature/module
- Separate concerns (controllers, services, models)
- Follow established naming conventions

**Version Control:**
- Meaningful commit messages
- Feature branch workflow
- Code review process

## 📊 **Success Metrics**

**Technical Metrics:**
- [e.g., API response time < 200ms]
- [e.g., 99% test coverage]
- [e.g., Zero security vulnerabilities]

**Business Metrics:**
- [e.g., User registration rate]
- [e.g., API usage statistics]
- [e.g., System uptime]

## 🚀 **Getting Started**

**Immediate Next Steps:**
1. **Project Setup**: Initialize project structure and dependencies
2. **Architecture Design**: Create system architecture diagram
3. **Database Design**: Design database schema and relationships
4. **API Design**: Define API endpoints and data models
5. **Authentication**: Implement user authentication system

**Phase 1 Deliverables:**
- [e.g., Basic project structure]
- [e.g., Database setup]
- [e.g., Authentication system]
- [e.g., Core API endpoints]

## 💡 **AI Assistant Instructions**

**How I want you to work:**

1. **Always ask clarifying questions** if requirements are unclear
2. **Create detailed implementation plans** before writing code
3. **Explain your decisions** and suggest alternatives when appropriate
4. **Write production-ready code** with proper error handling
5. **Provide comprehensive testing** for all functionality
6. **Document everything** including setup instructions and API docs
7. **Follow best practices** for security, performance, and maintainability
8. **Track progress** and summarize completed work

**Code Quality Expectations:**
- Write clean, readable, and maintainable code
- Include comprehensive error handling
- Add detailed comments for complex logic
- Follow TypeScript best practices
- Implement proper validation and sanitization
- Use consistent naming conventions

**Communication Style:**
- Be thorough but concise in explanations
- Provide examples and code snippets
- Suggest improvements and optimizations
- Alert me to potential issues or risks
- Ask for feedback and confirmation on major decisions

**Project Management:**
- Break large tasks into smaller, manageable pieces
- Provide progress updates after completing each phase
- Maintain a clear project roadmap
- Document decisions and changes
- Keep track of completed features and remaining work

## 🔍 **Current Status**

**What's Done:**
- [List any existing work or setup]

**What's Next:**
- [Immediate next steps]

**Questions for You:**
- [Any specific questions about the project]

---

**Ready to start! Please begin by:**
1. Reviewing this project overview
2. Asking any clarifying questions
3. Creating a detailed project structure and implementation plan
4. Setting up the initial development environment

Let's build something amazing together! 🚀

---

## 📝 **Template Usage Instructions**

### **How to Use This Template:**

1. **Replace Placeholders:**
   - [PROJECT_NAME] → Your actual project name
   - [PROJECT_TYPE] → Web app, mobile app, API, etc.
   - [Technology details] → Your preferred tech stack
   - [Features] → Your specific requirements

2. **Customize Sections:**
   - Add/remove features based on your needs
   - Adjust technical requirements
   - Modify success metrics
   - Update development preferences

3. **Example Filled Template:**
   ```
   Project Name: TaskMaster Pro
   Project Type: Task Management Web Application
   Technology Stack: NestJS + React + PostgreSQL
   Database: PostgreSQL with TypeORM
   Deployment: Docker + AWS
   
   Project Description:
   A comprehensive task management system for teams with real-time collaboration, 
   project tracking, and advanced reporting features.
   ```

### **Tips for Better Results:**

1. **Be Specific:** The more details you provide, the better the AI can help
2. **Set Clear Expectations:** Define what "done" looks like for each phase
3. **Include Context:** Mention any constraints, deadlines, or special requirements
4. **Ask Questions:** Don't hesitate to ask for clarification or alternatives
5. **Iterate:** Refine requirements as the project evolves

### **Common Project Types & Customizations:**

**For E-commerce:**
- Add payment processing requirements
- Include inventory management
- Specify shipping integrations

**For SaaS Applications:**
- Include subscription management
- Add multi-tenancy requirements
- Specify billing and analytics

**For APIs:**
- Focus on endpoint design
- Include rate limiting requirements
- Specify documentation standards

**For Mobile Apps:**
- Add platform requirements (iOS/Android)
- Include offline functionality
- Specify push notification needs
}