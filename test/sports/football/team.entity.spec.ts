import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { DataSource } from 'typeorm';
import configuration from '../../../src/core/config/configuration';
import { Team } from '../../../src/sports/football/models/team.entity';
import { DatabaseService } from '../../../src/core/database/database.service';

describe('Team Entity', () => {
  let dataSource: DataSource;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ load: [configuration] }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useClass: DatabaseService,
        }),
        TypeOrmModule.forFeature([Team]),
      ],
    }).compile();

    dataSource = module.get(DataSource);
    await dataSource.synchronize(true); // Reset DB
  }, 15000);

  afterAll(async () => {
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
  });

  it('should save and retrieve team', async () => {
    const teamData: Partial<Team> = {
      externalId: 33,
      name: 'Manchester United',
      logo: '/public/images/teams/33.png',
    };

    const repository = dataSource.getRepository(Team);
    const savedTeam = await repository.save(teamData);
    const retrievedTeam = await repository.findOneBy({ externalId: 33 });

    expect(retrievedTeam).toBeDefined();
    expect(retrievedTeam?.externalId).toBe(33);
    expect(retrievedTeam?.name).toBe('Manchester United');
    expect(retrievedTeam?.logo).toBe('/public/images/teams/33.png');
    expect(retrievedTeam?.createdAt.toISOString()).toMatch(/Z$/); // UTC
  });
});