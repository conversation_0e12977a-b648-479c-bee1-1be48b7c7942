import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { DataSource } from 'typeorm';
import configuration from '../../../src/core/config/configuration';
import { SystemUser } from '../../../src/auth/entities/system-user.entity';
import { DatabaseService } from '../../../src/core/database/database.service';

describe('SystemUser Entity', () => {
  let dataSource: DataSource;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ load: [configuration] }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useClass: DatabaseService,
        }),
        TypeOrmModule.forFeature([SystemUser]),
      ],
    }).compile();

    dataSource = module.get(DataSource);
    await dataSource.synchronize(true); // Reset DB
  }, 10000); // Tăng timeout lên 10s

  afterAll(async () => {
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
  });

  it('should save and retrieve system user', async () => {
    const userData: Partial<SystemUser> = {
      username: 'admin1',
      email: '<EMAIL>',
      passwordHash: '$2b$10$examplehash',
      fullName: 'Admin User',
      role: 'admin' as const, // Sửa: Dùng literal type
      isActive: true,
    };

    const repository = dataSource.getRepository(SystemUser);
    const savedUser = await repository.save(userData);
    const retrievedUser = await repository.findOneBy({ username: 'admin1' });

    expect(retrievedUser).toBeDefined();
    expect(retrievedUser?.email).toBe('<EMAIL>');
    expect(retrievedUser?.createdAt.toISOString()).toMatch(/Z$/); // UTC
  });
});