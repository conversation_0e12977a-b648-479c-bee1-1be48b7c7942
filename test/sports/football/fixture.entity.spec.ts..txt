import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Fixture } from '../../../src/sports/football/models/fixture.entity';

describe('Fixture Entity', () => {
  let repository: Repository<Fixture>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: getRepositoryToken(Fixture),
          useClass: Repository,
        },
      ],
    }).compile();

    repository = module.get<Repository<Fixture>>(getRepositoryToken(Fixture));
  });

  it('should create a fixture entity with valid data', async () => {
    const fixtureData: Partial<Fixture> = {
      externalId: 1354626,
      leagueId: 850,
      leagueName: 'UEFA U21 Championship - Qualification',
      season: 2025,
      round: 'Regular Season - 9',
      homeTeamId: 33,
      awayTeamId: 40,
      slug: 'manchester-united-vs-chelsea-2023-10-20',
      date: new Date('2023-10-20T14:00:00Z'),
      venueId: 465,
      venueName: 'Estadio Olímpico Atahualpa',
      venueCity: 'Quito',
      referee: 'John Doe',
      source: 'api',
      createdBy: null,
      timestamp: **********,
      data: {
        homeTeamName: 'Manchester United',
        homeTeamLogo: '/public/images/teams/33.png',
        awayTeamName: 'Chelsea',
        awayTeamLogo: '/public/images/teams/40.png',
        status: 'FT',
        statusLong: 'Match Finished', // Thêm
        statusExtra: 5, // Thêm
        elapsed: 90,
        goalsHome: 2,
        goalsAway: 1,
        scoreHalftimeHome: 1,
        scoreHalftimeAway: 0,
        scoreFulltimeHome: 2,
        scoreFulltimeAway: 1,
        periods: {
          first: 1747267200,
          second: 1747270800,
        },
      },
    };

    jest.spyOn(repository, 'create').mockReturnValue(fixtureData as Fixture);
    jest.spyOn(repository, 'save').mockResolvedValue(fixtureData as Fixture);

    const fixture = repository.create(fixtureData);
    const savedFixture = await repository.save(fixture);

    expect(fixture).toEqual(fixtureData);
    expect(savedFixture).toEqual(fixtureData);
    expect(savedFixture.data).toMatchObject({
      status: 'FT',
      statusLong: 'Match Finished',
      statusExtra: 5,
    });
  });

  // Các test case khác (nếu có) cũng cần cập nhật tương tự
});