import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { FixtureService } from '../../../src/sports/football/services/fixture.service';
import { Fixture } from '../../../src/sports/football/models/fixture.entity';
import { League } from '../../../src/sports/football/models/league.entity';
import { Team } from '../../../src/sports/football/models/team.entity';
import { CacheService } from '../../../src/core/cache/cache.service';
import { ImageService } from '../../../src/shared/services/image.service';
import { UtilsService } from '../../../src/shared/services/utils.service';
import { Repository, DataSource, EntityManager, InsertResult, In } from 'typeorm';
import { GetFixturesDto, PaginatedFixturesResponse, CreateFixtureDto, UpdateFixtureDto } from '../../../src/sports/football/models/fixture.dto';
import axios from 'axios';

jest.mock('axios');

describe('FixtureService', () => {
    let service: FixtureService;
    let fixtureRepository: Repository<Fixture>;
    let leagueRepository: Repository<League>;
    let teamRepository: Repository<Team>;
    let cacheService: CacheService;
    let imageService: ImageService;
    let dataSource: DataSource;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                FixtureService,
                {
                    provide: getRepositoryToken(Fixture),
                    useClass: Repository,
                },
                {
                    provide: getRepositoryToken(League),
                    useClass: Repository,
                },
                {
                    provide: getRepositoryToken(Team),
                    useClass: Repository,
                },
                {
                    provide: ConfigService,
                    useValue: {
                        get: jest.fn().mockImplementation((key: string) => {
                            if (key === 'app.apiFootballUrl') return 'https://v3.football.api-sports.io';
                            if (key === 'app.apiFootballKey') return 'mock-key';
                            return undefined;
                        }),
                    },
                },
                {
                    provide: CacheService,
                    useValue: {
                        getCache: jest.fn(),
                        setCache: jest.fn(),
                        deleteByPattern: jest.fn(),
                    },
                },
                {
                    provide: ImageService,
                    useValue: {
                        downloadImage: jest.fn(),
                    },
                },
                {
                    provide: UtilsService,
                    useValue: {
                        generateSlug: jest.fn().mockImplementation((input, date) => `${input}-${date}`),
                        formatDate: jest.fn().mockReturnValue('2023-10-20'),
                    },
                },
                {
                    provide: DataSource,
                    useValue: {
                        transaction: jest.fn().mockImplementation(async (fn: (manager: EntityManager) => Promise<any>) => {
                            const manager = {
                                getRepository: jest.fn().mockImplementation((entity) => {
                                    if (entity === League) return leagueRepository;
                                    if (entity === Team) return teamRepository;
                                    return fixtureRepository;
                                }),
                            };
                            return fn(manager as unknown as EntityManager);
                        }),
                    },
                },
            ],
        }).compile();

        service = module.get<FixtureService>(FixtureService);
        fixtureRepository = module.get<Repository<Fixture>>(getRepositoryToken(Fixture));
        leagueRepository = module.get<Repository<League>>(getRepositoryToken(League));
        teamRepository = module.get<Repository<Team>>(getRepositoryToken(Team));
        cacheService = module.get<CacheService>(CacheService);
        imageService = module.get<ImageService>(ImageService);
        dataSource = module.get<DataSource>(DataSource);

        jest.clearAllMocks();
    });



    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should create a manual fixture', async () => {
        const createFixtureDto: CreateFixtureDto = {
            leagueId: 850,
            season: 2025,
            homeTeamId: 33,
            awayTeamId: 40,
            date: '2023-10-20T14:00:00Z',
            round: 'Regular Season - 9',
            venueId: 465,
            venueName: 'Estadio Olímpico Atahualpa',
            venueCity: 'Quito',
            referee: 'John Doe',
            isHot: true,
            timestamp: 1697810400,
            data: {
                homeTeamName: 'Manchester United',
                homeTeamLogo: '/public/images/teams/33.png',
                awayTeamName: 'Chelsea',
                awayTeamLogo: '/public/images/teams/40.png',
                status: 'FT',
                statusLong: 'Match Finished',
                statusExtra: 5,
                elapsed: 90,
                goalsHome: 2,
                goalsAway: 1,
                scoreHalftimeHome: 1,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 2,
                scoreFulltimeAway: 1,
                periods: {
                    first: 1747267200,
                    second: 1747270800,
                },
            },
        };
        const savedFixture: Fixture = {
            id: 1,
            externalId: 0,
            leagueId: 850,
            leagueName: 'UEFA U21 Championship - Qualification',
            season: 2025,
            round: 'Regular Season - 9',
            homeTeamId: 33,
            awayTeamId: 40,
            slug: 'Manchester United-vs-Chelsea-2023-10-20T14:00:00Z',
            date: new Date('2023-10-20T14:00:00Z'),
            venueId: 465,
            venueName: 'Estadio Olímpico Atahualpa',
            venueCity: 'Quito',
            referee: 'John Doe',
            source: 'manual',
            createdBy: null,
            isHot: true,
            timestamp: 1697810400,
            data: {
                homeTeamName: 'Manchester United',
                homeTeamLogo: '/public/images/teams/33.png',
                awayTeamName: 'Chelsea',
                awayTeamLogo: '/public/images/teams/40.png',
                status: 'FT',
                statusLong: 'Match Finished',
                statusExtra: 5,
                elapsed: 90,
                goalsHome: 2,
                goalsAway: 1,
                scoreHalftimeHome: 1,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 2,
                scoreFulltimeAway: 1,
                periods: {
                    first: 1747267200,
                    second: 1747270800,
                },
            },
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        };
        jest.spyOn(leagueRepository, 'findOne').mockResolvedValueOnce({
            externalId: 850,
            name: 'UEFA U21 Championship - Qualification',
            season: 2025,
        } as League);
        jest.spyOn(teamRepository, 'findOneBy').mockResolvedValueOnce({
            externalId: 33,
            name: 'Manchester United',
            logo: '/public/images/teams/33.png',
        } as Team);
        jest.spyOn(teamRepository, 'findOneBy').mockResolvedValueOnce({
            externalId: 40,
            name: 'Chelsea',
            logo: '/public/images/teams/40.png',
        } as Team);
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(null);
        jest.spyOn(fixtureRepository, 'save').mockResolvedValueOnce(savedFixture);
        jest.spyOn(cacheService, 'deleteByPattern').mockResolvedValueOnce();

        const result = await service.createFixture(createFixtureDto);

        expect(leagueRepository.findOne).toHaveBeenCalledWith({
            where: { externalId: 850, season: 2025 },
        });
        expect(teamRepository.findOneBy).toHaveBeenCalledWith({ externalId: 33 });
        expect(teamRepository.findOneBy).toHaveBeenCalledWith({ externalId: 40 });
        expect(fixtureRepository.findOneBy).toHaveBeenCalledWith({ slug: 'Manchester United-vs-Chelsea-2023-10-20T14:00:00Z' });
        expect(fixtureRepository.save).toHaveBeenCalledWith(expect.any(Fixture));
        expect(cacheService.deleteByPattern).toHaveBeenCalledWith('fixtures_list_850_*');
        expect(result).toMatchObject({
            id: 1,
            leagueId: 850,
            homeTeamId: 33,
            awayTeamId: 40,
            slug: 'Manchester United-vs-Chelsea-2023-10-20T14:00:00Z',
            status: 'FT',
            statusLong: 'Match Finished',
            statusExtra: 5,
        });
    });
    it('should update a fixture', async () => {
        const externalId = 1354626;
        const updateFixtureDto: UpdateFixtureDto = {
            data: {
                status: 'FT',
                statusLong: 'Match Finished',
                statusExtra: 5,
                goalsHome: 3,
                goalsAway: 2,
            },
        };
        const existingFixture = new Fixture();
        Object.assign(existingFixture, {
            id: 1,
            externalId: 1354626,
            leagueId: 850,
            leagueName: 'UEFA U21 Championship - Qualification',
            season: 2025,
            round: 'Regular Season - 9',
            homeTeamId: 33,
            awayTeamId: 40,
            slug: 'manchester-united-vs-chelsea-2023-10-20',
            date: new Date('2023-10-20T14:00:00Z'),
            venueId: 465,
            isHot: true,
            venueName: 'Estadio Olímpico Atahualpa',
            venueCity: 'Quito',
            referee: 'John Doe',
            source: 'manual',
            createdBy: null,
            timestamp: 1697810400,
            data: {
                homeTeamName: 'Manchester United',
                homeTeamLogo: '/public/images/teams/33.png',
                awayTeamName: 'Chelsea',
                awayTeamLogo: '/public/images/teams/40.png',
                status: 'NS',
                statusLong: 'Not Started',
                statusExtra: 0,
                elapsed: 0,
                goalsHome: 0,
                goalsAway: 0,
                scoreHalftimeHome: 0,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 0,
                scoreFulltimeAway: 0,
                periods: {
                    first: 0,
                    second: 0,
                },
            },
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        const updatedFixture = new Fixture();
        Object.assign(updatedFixture, {
            ...existingFixture,
            data: {
                ...existingFixture.data,
                status: 'FT',
                statusLong: 'Match Finished',
                statusExtra: 5,
                goalsHome: 3,
                goalsAway: 2,
            },
            updatedAt: new Date('2025-05-14T11:00:00Z'),
        });
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(existingFixture);
        jest.spyOn(fixtureRepository, 'save').mockResolvedValueOnce(updatedFixture);
        jest.spyOn(cacheService, 'deleteByPattern').mockResolvedValueOnce();

        const result = await service.updateFixture(externalId, updateFixtureDto);

        expect(fixtureRepository.findOneBy).toHaveBeenCalledWith({ externalId });
        expect(fixtureRepository.save).toHaveBeenCalledWith(expect.any(Fixture));
        expect(cacheService.deleteByPattern).toHaveBeenCalledWith('fixtures_list_850_*');
        expect(result).toMatchObject({
            id: 1,
            externalId: 1354626,
            leagueId: 850,
            homeTeamId: 33,
            awayTeamId: 40,
            status: 'FT',
            statusLong: 'Match Finished',
            statusExtra: 5,
            goalsHome: 3,
            goalsAway: 2,
        });
    });

    it('should throw error if fixture not found', async () => {
        const externalId = 1354626;
        const updateFixtureDto: UpdateFixtureDto = {
            data: { status: 'FT' },
        };
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(null);

        await expect(service.updateFixture(externalId, updateFixtureDto)).rejects.toThrow(
            `Fixture with externalId ${externalId} not found`,
        );
    });

    it('should throw error if league not found', async () => {
        const externalId = 1354626;
        const updateFixtureDto: UpdateFixtureDto = {
            leagueId: 850,
            season: 2025,
        };
        const existingFixture = new Fixture();
        Object.assign(existingFixture, {
            id: 1,
            externalId: 1354626,
            leagueId: 39,
            leagueName: 'Premier League',
            season: 2023,
            round: 'Regular Season - 9',
            homeTeamId: 33,
            awayTeamId: 40,
            slug: 'manchester-united-vs-chelsea-2023-10-20',
            date: new Date('2023-10-20T14:00:00Z'),
            venueId: 465,
            venueName: 'Estadio Olímpico Atahualpa',
            venueCity: 'Quito',
            referee: 'John Doe',
            source: 'manual',
            createdBy: null,
            timestamp: 1697810400,
            data: {
                homeTeamName: 'Manchester United',
                homeTeamLogo: '/public/images/teams/33.png',
                awayTeamName: 'Chelsea',
                awayTeamLogo: '/public/images/teams/40.png',
                status: 'NS',
                statusLong: 'Not Started',
                statusExtra: 0,
                elapsed: 0,
                goalsHome: 0,
                goalsAway: 0,
                scoreHalftimeHome: 0,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 0,
                scoreFulltimeAway: 0,
                periods: { first: 0, second: 0 },
            },
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(existingFixture);
        jest.spyOn(leagueRepository, 'findOne').mockResolvedValueOnce(null);

        await expect(service.updateFixture(externalId, updateFixtureDto)).rejects.toThrow(
            `League with externalId 850 and season 2025 not found`,
        );
    });

    it('should fetch from DB with pagination and isHot filter', async () => {
        const query: GetFixturesDto = { league: 39, season: 2023, page: 2, limit: 1, isHot: true };
        const fixtures = [
            {
                id: 2,
                externalId: 1354627,
                leagueId: 39,
                leagueName: 'Premier League',
                season: 2023,
                round: 'Regular Season - 9',
                homeTeamId: 33,
                awayTeamId: 41,
                isHot: true,
                slug: 'manchester-united-vs-arsenal-2023-10-20',
                date: new Date('2023-10-20T16:00:00Z'),
                venueId: 466,
                venueName: 'Stadium B',
                venueCity: 'London',
                referee: 'Jane Doe',
                source: 'api' as const,
                createdBy: null,
                timestamp: 1697817600,
                data: {
                    homeTeamName: 'Manchester United',
                    homeTeamLogo: '/public/images/teams/33.png',
                    awayTeamName: 'Arsenal',
                    awayTeamLogo: '/public/images/teams/41.png',
                    status: 'FT',
                    statusLong: 'Match Finished',
                    statusExtra: 3,
                    elapsed: 90,
                    goalsHome: 1,
                    goalsAway: 1,
                    scoreHalftimeHome: 0,
                    scoreHalftimeAway: 0,
                    scoreFulltimeHome: 1,
                    scoreFulltimeAway: 1,
                    periods: { first: 1747274400, second: 1747278000 },
                },
                createdAt: new Date('2025-05-14T10:00:00Z'),
                updatedAt: new Date('2025-05-14T10:00:00Z'),
            },
        ];
        const expectedResponse: PaginatedFixturesResponse = {
            data: fixtures.map((fixture) => ({
                id: fixture.id,
                externalId: fixture.externalId,
                leagueId: fixture.leagueId,
                leagueName: fixture.leagueName,
                isHot: fixture.isHot, // Đặt trước season để khớp thứ tự
                season: fixture.season,
                round: fixture.round,
                homeTeamId: fixture.homeTeamId,
                homeTeamName: fixture.data.homeTeamName,
                homeTeamLogo: fixture.data.homeTeamLogo,
                awayTeamId: fixture.awayTeamId,
                awayTeamName: fixture.data.awayTeamName,
                awayTeamLogo: fixture.data.awayTeamLogo,
                slug: fixture.slug,
                date: fixture.date.toISOString(),
                venue: {
                    id: fixture.venueId,
                    name: fixture.venueName,
                    city: fixture.venueCity,
                },
                referee: fixture.referee,
                status: fixture.data.status,
                statusLong: fixture.data.statusLong,
                statusExtra: fixture.data.statusExtra,
                elapsed: fixture.data.elapsed,
                goalsHome: fixture.data.goalsHome,
                goalsAway: fixture.data.goalsAway,
                scoreHalftimeHome: fixture.data.scoreHalftimeHome,
                scoreHalftimeAway: fixture.data.scoreHalftimeAway,
                scoreFulltimeHome: fixture.data.scoreFulltimeHome,
                scoreFulltimeAway: fixture.data.scoreFulltimeAway,
                periods: fixture.data.periods,
                timestamp: fixture.timestamp,
            })),
            meta: {
                totalItems: 3,
                totalPages: 3,
                currentPage: 2,
                limit: 1,
            },
            status: 200,
        };

        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(fixtureRepository, 'createQueryBuilder').mockReturnValue({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([fixtures, 3]),
        } as any);

        const result = await service.getFixtures(query);

        expect(fixtureRepository.createQueryBuilder).toHaveBeenCalledWith('fixture');
        expect(result).toMatchObject({
            data: expect.arrayContaining([
                expect.objectContaining({
                    id: 2,
                    externalId: 1354627,
                    leagueId: 39,
                    isHot: true,
                    status: 'FT',
                    statusLong: 'Match Finished',
                    statusExtra: 3,
                }),
            ]),
            meta: {
                totalItems: 3,
                totalPages: 3,
                currentPage: 2,
                limit: 1,
            },
            status: 200,
        });
        expect(result.data).toHaveLength(1);
        expect(cacheService.setCache).toHaveBeenCalledWith(
            'fixtures_list_39_2023___true_2_1',
            JSON.stringify(expectedResponse),
            3600,
        );
    });

    it('should fetch from API, process leagues and teams, and save fixtures', async () => {
        const query: GetFixturesDto = { league: 850, season: 2025, page: 1, limit: 10 };
        const apiData = [
            {
                fixture: {
                    id: 1354626,
                    referee: 'John Doe',
                    date: '2023-10-20T14:00:00+00:00',
                    timestamp: 1697810400,
                    venue: { id: 465, name: 'Estadio Olímpico Atahualpa', city: 'Quito' },
                    status: { short: 'FT', long: 'Match Finished', extra: 5, elapsed: 90 },
                    periods: { first: 1747267200, second: 1747270800 },
                },
                league: {
                    id: 850,
                    name: 'UEFA U21 Championship - Qualification',
                    country: 'World',
                    logo: 'https://media.api-sports.io/football/leagues/850.png',
                    flag: '',
                    season: 2025,
                    round: 'Regular Season - 9',
                },
                teams: {
                    home: { id: 33, name: 'Manchester United', logo: 'https://media.api-sports.io/football/teams/33.png' },
                    away: { id: 40, name: 'Chelsea', logo: 'https://media.api-sports.io/football/teams/40.png' },
                },
                goals: { home: 2, away: 1 },
                score: { halftime: { home: 1, away: 0 }, fulltime: { home: 2, away: 1 } },
            },
        ];
        const savedFixture: Partial<Fixture> = {
            id: 1,
            externalId: 1354626,
            leagueId: 850,
            leagueName: 'UEFA U21 Championship - Qualification',
            season: 2025,
            round: 'Regular Season - 9',
            homeTeamId: 33,
            awayTeamId: 40,
            slug: 'manchester-united-vs-chelsea-2023-10-20',
            date: new Date('2023-10-20T14:00:00Z'),
            venueId: 465,
            venueName: 'Estadio Olímpico Atahualpa',
            venueCity: 'Quito',
            referee: 'John Doe',
            source: 'api',
            createdBy: null,
            timestamp: 1697810400,
            data: {
                homeTeamName: 'Manchester United',
                homeTeamLogo: '/public/images/teams/33.png',
                awayTeamName: 'Chelsea',
                awayTeamLogo: '/public/images/teams/40.png',
                status: 'FT',
                statusLong: 'Match Finished',
                statusExtra: 5,
                elapsed: 90,
                goalsHome: 2,
                goalsAway: 1,
                scoreHalftimeHome: 1,
                scoreHalftimeAway: 0,
                scoreFulltimeHome: 2,
                scoreFulltimeAway: 1,
                periods: {
                    first: 1747267200,
                    second: 1747270800,
                },
            },
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        };
        const savedLeague: Partial<League> = {
            externalId: 850,
            name: 'UEFA U21 Championship - Qualification',
            country: 'World',
            logo: '/public/images/leagues/850.png',
            flag: '',
            season: 2025,
        };
        const savedHomeTeam: Partial<Team> = {
            externalId: 33,
            name: 'Manchester United',
            logo: '/public/images/teams/33.png',
        };
        const savedAwayTeam: Partial<Team> = {
            externalId: 40,
            name: 'Chelsea',
            logo: '/public/images/teams/40.png',
        };

        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(fixtureRepository, 'createQueryBuilder').mockReturnValueOnce({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        } as any);
        jest.spyOn(leagueRepository, 'findBy').mockResolvedValueOnce([]);
        jest.spyOn(leagueRepository, 'upsert').mockResolvedValueOnce({
            identifiers: [{ externalId: 850, season: 2025 }],
            generatedMaps: [],
            raw: [],
        } as InsertResult);
        jest.spyOn(teamRepository, 'findBy').mockResolvedValueOnce([]);
        jest.spyOn(teamRepository, 'upsert').mockResolvedValueOnce({
            identifiers: [{ externalId: 33 }, { externalId: 40 }],
            generatedMaps: [],
            raw: [],
        } as InsertResult);
        jest.spyOn(axios, 'get').mockResolvedValueOnce({ data: { response: apiData } });
        jest.spyOn(fixtureRepository, 'save').mockResolvedValueOnce([savedFixture] as any);
        jest.spyOn(imageService, 'downloadImage').mockImplementation((url: string) => {
            if (url.includes('leagues/850.png')) return Promise.resolve('/public/images/leagues/850.png');
            if (url.includes('teams/33.png')) return Promise.resolve('/public/images/teams/33.png');
            if (url.includes('teams/40.png')) return Promise.resolve('/public/images/teams/40.png');
            return Promise.resolve('');
        });
        jest.spyOn(fixtureRepository, 'createQueryBuilder').mockReturnValueOnce({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([[savedFixture], 1]),
        } as any);

        const result = await service.getFixtures(query);

        expect(axios.get).toHaveBeenCalledWith(
            'https://v3.football.api-sports.io/fixtures',
            expect.any(Object),
        );
        expect(leagueRepository.findBy).toHaveBeenCalledWith({
            externalId: expect.objectContaining({ _type: 'in', _value: expect.arrayContaining([850]) }),
            season: expect.objectContaining({ _type: 'in', _value: expect.arrayContaining([2025]) }),
        });
        expect(leagueRepository.upsert).toHaveBeenCalledWith(
            expect.arrayContaining([expect.objectContaining(savedLeague)]),
            ['externalId', 'season'],
        );
        expect(teamRepository.findBy).toHaveBeenCalledWith({
            externalId: expect.objectContaining({ _type: 'in', _value: expect.arrayContaining([33, 40]) }),
        });
        expect(teamRepository.upsert).toHaveBeenCalledWith(
            expect.arrayContaining([
                expect.objectContaining(savedHomeTeam),
                expect.objectContaining(savedAwayTeam),
            ]),
            ['externalId'],
        );
        expect(dataSource.transaction).toHaveBeenCalled();
        expect(fixtureRepository.save).toHaveBeenCalled();
        expect(cacheService.setCache).toHaveBeenCalled();
        expect(result.data).toHaveLength(1);
        expect(result.data[0]).toMatchObject({
            date: '2023-10-20T14:00:00.000Z',
            status: 'FT',
            statusLong: 'Match Finished',
            statusExtra: 5,
            homeTeamLogo: '/public/images/teams/33.png',
            awayTeamLogo: '/public/images/teams/40.png',
        });
        expect(result.meta).toMatchObject({
            totalItems: 1,
            totalPages: 1,
            currentPage: 1,
            limit: 10,
        });
    });

    it('should handle duplicate leagues and teams', async () => {
        const query: GetFixturesDto = { league: 850, season: 2025, page: 1, limit: 2 };
        const apiData = [
            {
                fixture: {
                    id: 1354626,
                    referee: 'John Doe',
                    date: '2023-10-20T14:00:00+00:00',
                    timestamp: 1697810400,
                    venue: { id: 465, name: 'Estadio Olímpico Atahualpa', city: 'Quito' },
                    status: { short: 'FT', long: 'Match Finished', extra: 5, elapsed: 90 },
                    periods: { first: 1747267200, second: 1747270800 },
                },
                league: {
                    id: 850,
                    name: 'UEFA U21 Championship - Qualification',
                    country: 'World',
                    logo: 'https://media.api-sports.io/football/leagues/850.png',
                    flag: '',
                    season: 2025,
                    round: 'Regular Season - 9',
                },
                teams: {
                    home: { id: 33, name: 'Manchester United', logo: 'https://media.api-sports.io/football/teams/33.png' },
                    away: { id: 40, name: 'Chelsea', logo: 'https://media.api-sports.io/football/teams/40.png' },
                },
                goals: { home: 2, away: 1 },
                score: { halftime: { home: 1, away: 0 }, fulltime: { home: 2, away: 1 } },
            },
            {
                fixture: {
                    id: 1354627,
                    referee: 'Jane Doe',
                    date: '2023-10-20T16:00:00+00:00',
                    timestamp: 1697817600,
                    venue: { id: 466, name: 'Stadium B', city: 'London' },
                    status: { short: 'FT', long: 'Match Finished', extra: 3, elapsed: 90 },
                    periods: { first: 1747274400, second: 1747278000 },
                },
                league: {
                    id: 850,
                    name: 'UEFA U21 Championship - Qualification',
                    country: 'World',
                    logo: 'https://media.api-sports.io/football/leagues/850.png',
                    flag: '',
                    season: 2025,
                    round: 'Regular Season - 9',
                },
                teams: {
                    home: { id: 33, name: 'Manchester United', logo: 'https://media.api-sports.io/football/teams/33.png' },
                    away: { id: 41, name: 'Arsenal', logo: 'https://media.api-sports.io/football/teams/41.png' },
                },
                goals: { home: 1, away: 1 },
                score: { halftime: { home: 0, away: 0 }, fulltime: { home: 1, away: 1 } },
            },
        ];
        const savedLeague: Partial<League> = {
            externalId: 850,
            name: 'UEFA U21 Championship - Qualification',
            country: 'World',
            logo: '/public/images/leagues/850.png',
            flag: '',
            season: 2025,
        };
        const savedHomeTeam: Partial<Team> = {
            externalId: 33,
            name: 'Manchester United',
            logo: '/public/images/teams/33.png',
        };
        const savedAwayTeam1: Partial<Team> = {
            externalId: 40,
            name: 'Chelsea',
            logo: '/public/images/teams/40.png',
        };
        const savedAwayTeam2: Partial<Team> = {
            externalId: 41,
            name: 'Arsenal',
            logo: '/public/images/teams/41.png',
        };
        const savedFixtures = apiData.map(data => ({
            id: data.fixture.id,
            externalId: data.fixture.id,
            leagueId: data.league.id,
            leagueName: data.league.name,
            season: data.league.season,
            round: data.league.round,
            homeTeamId: data.teams.home.id,
            awayTeamId: data.teams.away.id,
            slug: `match-${data.fixture.id}`,
            date: new Date(data.fixture.date),
            venueId: data.fixture.venue.id,
            venueName: data.fixture.venue.name,
            venueCity: data.fixture.venue.city,
            referee: data.fixture.referee,
            source: 'api' as const,
            createdBy: null,
            timestamp: data.fixture.timestamp,
            data: {
                homeTeamName: data.teams.home.name,
                homeTeamLogo: `/public/images/teams/${data.teams.home.id}.png`,
                awayTeamName: data.teams.away.name,
                awayTeamLogo: `/public/images/teams/${data.teams.away.id}.png`,
                status: data.fixture.status.short,
                statusLong: data.fixture.status.long,
                statusExtra: data.fixture.status.extra,
                elapsed: data.fixture.status.elapsed,
                goalsHome: data.goals.home,
                goalsAway: data.goals.away,
                scoreHalftimeHome: data.score.halftime.home,
                scoreHalftimeAway: data.score.halftime.away,
                scoreFulltimeHome: data.score.fulltime.home,
                scoreFulltimeAway: data.score.fulltime.away,
                periods: data.fixture.periods,
            },
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        }));

        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(fixtureRepository, 'createQueryBuilder').mockReturnValueOnce({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        } as any);
        jest.spyOn(leagueRepository, 'findBy').mockResolvedValueOnce([savedLeague as League]);
        jest.spyOn(teamRepository, 'findBy').mockResolvedValueOnce([savedHomeTeam as Team]);
        jest.spyOn(teamRepository, 'upsert').mockResolvedValueOnce({
            identifiers: [{ externalId: 40 }, { externalId: 41 }],
            generatedMaps: [],
            raw: [],
        } as InsertResult);
        jest.spyOn(axios, 'get').mockResolvedValueOnce({ data: { response: apiData } });
        jest.spyOn(fixtureRepository, 'save').mockResolvedValueOnce(savedFixtures as any);
        jest.spyOn(imageService, 'downloadImage').mockImplementation((url: string) => {
            if (url.includes('leagues/850.png')) return Promise.resolve('/public/images/leagues/850.png');
            if (url.includes('flags/850.svg')) return Promise.resolve('');
            if (url.includes('teams/33.png')) return Promise.resolve('/public/images/teams/33.png');
            if (url.includes('teams/40.png')) return Promise.resolve('/public/images/teams/40.png');
            if (url.includes('teams/41.png')) return Promise.resolve('/public/images/teams/41.png');
            return Promise.resolve('');
        });
        jest.spyOn(fixtureRepository, 'createQueryBuilder').mockReturnValueOnce({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([savedFixtures, 2]),
        } as any);

        const result = await service.getFixtures(query);

        expect(leagueRepository.findBy).toHaveBeenCalledWith({
            externalId: expect.objectContaining({ _type: 'in', _value: expect.arrayContaining([850]) }),
            season: expect.objectContaining({ _type: 'in', _value: expect.arrayContaining([2025]) }),
        });
        expect(teamRepository.findBy).toHaveBeenCalledWith({
            externalId: expect.objectContaining({ _type: 'in', _value: expect.arrayContaining([33, 40, 41]) }),
        });
        expect(teamRepository.upsert).toHaveBeenCalledWith(
            expect.arrayContaining([
                expect.objectContaining(savedAwayTeam1),
                expect.objectContaining(savedAwayTeam2),
            ]),
            ['externalId'],
        );
        expect(imageService.downloadImage).toHaveBeenCalledTimes(6); // 1 league logo + 2 league flags + 3 team logos
        expect(result.data).toHaveLength(2);
        expect(result.data[0]).toMatchObject({
            status: 'FT',
            statusLong: 'Match Finished',
            statusExtra: 5,
        });
        expect(result.data[1]).toMatchObject({
            status: 'FT',
            statusLong: 'Match Finished',
            statusExtra: 3,
        });
        expect(result.meta).toMatchObject({
            totalItems: 2,
            totalPages: 1,
            currentPage: 1,
            limit: 2,
        });
    });

    it('should retry on API failure', async () => {
        const query: GetFixturesDto = { league: 850, season: 2025, page: 1, limit: 10 };
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(fixtureRepository, 'createQueryBuilder').mockReturnValue({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        } as any);
        jest.spyOn(axios, 'get')
            .mockRejectedValueOnce(new Error('Network error'))
            .mockRejectedValueOnce(new Error('Network error'))
            .mockResolvedValueOnce({ data: { response: [] } });
        jest.spyOn(leagueRepository, 'findBy').mockResolvedValueOnce([]);
        jest.spyOn(teamRepository, 'findBy').mockResolvedValueOnce([]);

        const result = await service.getFixtures(query);

        expect(axios.get).toHaveBeenCalledTimes(3);
        expect(result.data).toEqual([]);
        expect(result.meta).toMatchObject({
            totalItems: 0,
            totalPages: 0,
            currentPage: 1,
            limit: 10,
        });
    });
});