import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../src/app.module';

describe('LeagueController (e2e)', () => {
    let app: INestApplication;

    beforeEach(async () => {
        const moduleFixture: TestingModule = await Test.createTestingModule({
            imports: [AppModule],
        }).compile();

        app = moduleFixture.createNestApplication();
        await app.init();
    });

    it('/football/leagues (GET) should return leagues', () => {
        return request(app.getHttpServer())
            .get('/football/leagues')
            .expect(200)
            .expect((res) => {
                expect(Array.isArray(res.body)).toBe(true);
                if (res.body.length > 0) {
                    expect(res.body[0]).toHaveProperty('id');
                    expect(res.body[0]).toHaveProperty('name');
                }
            });
    });

    it('/football/leagues?country=England (GET) should filter by country', () => {
        return request(app.getHttpServer())
            .get('/football/leagues?country=England')
            .expect(200)
            .expect((res) => {
                expect(res.body.every((league: any) => league.country === 'England')).toBe(true);
            });
    });
});
