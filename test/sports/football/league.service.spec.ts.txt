import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LeagueService } from '../../../src/sports/football/services/league.service';
import { League } from '../../../src/sports/football/models/league.entity';
import { ConfigService } from '@nestjs/config';
import { CacheService } from '../../../src/core/cache/cache.service';
import { ImageService } from '../../../src/shared/services/image.service';
import { CreateLeagueDto, UpdateLeagueDto, GetLeaguesDto, PaginatedLeaguesResponse } from '../../../src/sports/football/models/league.dto';
import axios from 'axios';

jest.mock('axios');

describe('LeagueService', () => {
    let service: LeagueService;
    let leagueRepository: Repository<League>;
    let cacheService: CacheService;
    let imageService: ImageService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                LeagueService,
                {
                    provide: getRepositoryToken(League),
                    useValue: {
                        createQueryBuilder: jest.fn(),
                        findOneBy: jest.fn(),
                        create: jest.fn(), // Sửa lỗi: thay "create Taxes" thành "create"
                        save: jest.fn(),
                        delete: jest.fn(),
                    },
                },
                {
                    provide: ConfigService,
                    useValue: {
                        get: jest.fn().mockImplementation((key: string) => {
                            if (key === 'app.apiFootballUrl') return 'https://v3.football.api-sports.io';
                            if (key === 'app.apiFootballKey') return 'mock-key';
                            return undefined;
                        }),
                    },
                },
                {
                    provide: CacheService,
                    useValue: {
                        getCache: jest.fn(),
                        setCache: jest.fn(),
                        deleteByPattern: jest.fn(),
                    },
                },
                {
                    provide: ImageService,
                    useValue: {
                        downloadImage: jest.fn(),
                    },
                },
            ],
        }).compile();

        service = module.get<LeagueService>(LeagueService);
        leagueRepository = module.get<Repository<League>>(getRepositoryToken(League));
        cacheService = module.get<CacheService>(CacheService);
        imageService = module.get<ImageService>(ImageService);
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should create a league manually', async () => {
        const createLeagueDto: CreateLeagueDto = {
            externalId: 999,
            name: 'Custom League',
            country: 'Vietnam',
            season: 2024,
            logo: 'https://example.com/custom.png',
            flag: 'https://example.com/vn.svg',
            active: false,
        };
        const savedLeague = new League();
        Object.assign(savedLeague, {
            id: 1,
            externalId: 999,
            name: 'Custom League',
            country: 'vietnam',
            season: 2024,
            logo: 'https://example.com/custom.png',
            flag: 'https://example.com/vn.svg',
            active: false,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        jest.spyOn(leagueRepository, 'findOneBy').mockResolvedValueOnce(null);
        jest.spyOn(leagueRepository, 'create').mockReturnValueOnce(savedLeague);
        jest.spyOn(leagueRepository, 'save').mockResolvedValueOnce(savedLeague);
        jest.spyOn(cacheService, 'deleteByPattern').mockResolvedValueOnce();

        const result = await service.createLeague(createLeagueDto);

        expect(leagueRepository.findOneBy).toHaveBeenCalledWith({
            externalId: 999,
            season: 2024,
        });
        expect(leagueRepository.create).toHaveBeenCalledWith({
            externalId: 999,
            name: 'Custom League',
            country: 'vietnam',
            season: 2024,
            logo: 'https://example.com/custom.png',
            flag: 'https://example.com/vn.svg',
            active: false,
        });
        expect(leagueRepository.save).toHaveBeenCalledWith(savedLeague);
        expect(cacheService.deleteByPattern).toHaveBeenCalledWith('leagues_list_*_2024_vietnam_*');
        expect(result).toMatchObject({
            id: 1,
            externalId: 999,
            name: 'Custom League',
            country: 'vietnam',
            season: 2024,
            logo: 'https://example.com/custom.png',
            flag: 'https://example.com/vn.svg',
            active: false,
        });
    });

    it('should throw error if league already exists when creating', async () => {
        const createLeagueDto: CreateLeagueDto = {
            externalId: 999,
            name: 'Custom League',
            country: 'Vietnam',
            season: 2024,
        };
        const existingLeague = new League();
        Object.assign(existingLeague, {
            id: 1,
            externalId: 999,
            season: 2024,
        });
        jest.spyOn(leagueRepository, 'findOneBy').mockResolvedValueOnce(existingLeague);

        await expect(service.createLeague(createLeagueDto)).rejects.toThrow(
            `League with externalId ${createLeagueDto.externalId} and season ${createLeagueDto.season} already exists`,
        );
    });

    it('should update a league', async () => {
        const id = 1;
        const updateLeagueDto: UpdateLeagueDto = {
            name: 'Updated League',
            country: 'Vietnam',
            active: false,
        };
        const existingLeague = new League();
        Object.assign(existingLeague, {
            id: 1,
            externalId: 322,
            name: 'V League 1',
            country: 'vietnam',
            logo: 'https://example.com/leagues/322.png',
            flag: 'https://example.com/vn.svg',
            season: 2024,
            active: true,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        const updatedLeague = new League();
        Object.assign(updatedLeague, {
            ...existingLeague,
            name: 'Updated League',
            country: 'vietnam',
            active: false,
            updatedAt: new Date('2025-05-14T11:00:00Z'),
        });
        jest.spyOn(leagueRepository, 'findOneBy').mockResolvedValueOnce(existingLeague);
        jest.spyOn(leagueRepository, 'save').mockResolvedValueOnce(updatedLeague);
        jest.spyOn(cacheService, 'deleteByPattern').mockResolvedValueOnce();

        const result = await service.updateLeague(id, updateLeagueDto);

        expect(leagueRepository.findOneBy).toHaveBeenCalledWith({ id });
        expect(leagueRepository.save).toHaveBeenCalledWith(expect.any(League));
        expect(cacheService.deleteByPattern).toHaveBeenCalledWith('leagues_list_*_2024_vietnam_*');
        expect(result).toMatchObject({
            id: 1,
            externalId: 322,
            name: 'Updated League',
            country: 'vietnam',
            season: 2024,
            active: false,
        });
    });

    it('should throw error if league not found when updating', async () => {
        const id = 1;
        const updateLeagueDto: UpdateLeagueDto = {
            name: 'Updated League',
        };
        jest.spyOn(leagueRepository, 'findOneBy').mockResolvedValueOnce(null);

        await expect(service.updateLeague(id, updateLeagueDto)).rejects.toThrow(
            `League with id ${id} not found`,
        );
    });

    it('should fetch leagues from cache with pagination', async () => {
        const query: GetLeaguesDto = { season: 2025, country: 'England', page: 1, limit: 2 };
        const cachedLeagues = [
            {
                id: 1,
                externalId: 39,
                name: 'Premier League',
                country: 'england',
                logo: 'https://example.com/leagues/39.png',
                flag: 'https://example.com/england.svg',
                season: 2025,
                active: true,
            },
            {
                id: 2,
                externalId: 40,
                name: 'Championship',
                country: 'england',
                logo: 'https://example.com/leagues/40.png',
                flag: 'https://example.com/england.svg',
                season: 2025,
                active: true,
            },
        ];
        const cachedResponse: PaginatedLeaguesResponse = {
            data: cachedLeagues,
            meta: {
                totalItems: 2,
                totalPages: 1,
                currentPage: 1,
                limit: 2,
            },
            status: 200,
        };
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(JSON.stringify(cachedResponse));

        const result = await service.getLeagues(query);

        expect(cacheService.getCache).toHaveBeenCalledWith('leagues_list_2025_England_all_1_2');
        expect(result).toEqual(cachedResponse);
        expect(result.data).toHaveLength(2);
    });

    it('should fetch leagues from DB with pagination', async () => {
        const query: GetLeaguesDto = { season: 2025, country: 'England', page: 1, limit: 1 };
        const leagues = [
            new League(),
        ];
        Object.assign(leagues[0], {
            id: 1,
            externalId: 39,
            name: 'Premier League',
            country: 'england',
            logo: 'https://example.com/leagues/39.png',
            flag: 'https://example.com/england.svg',
            season: 2025,
            active: true,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        const expectedResponse: PaginatedLeaguesResponse = {
            data: [
                {
                    id: 1,
                    externalId: 39,
                    name: 'Premier League',
                    country: 'england',
                    logo: 'https://example.com/leagues/39.png',
                    flag: 'https://example.com/england.svg',
                    season: 2025,
                    type: 'league',
                    active: true,
                },
            ],
            meta: {
                totalItems: 2,
                totalPages: 2,
                currentPage: 1,
                limit: 1,
            },
            status: 200,
        };
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(leagueRepository, 'createQueryBuilder').mockReturnValue({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([leagues, 2]),
        } as any);
        jest.spyOn(cacheService, 'setCache').mockResolvedValueOnce();

        const result = await service.getLeagues(query);

        expect(leagueRepository.createQueryBuilder).toHaveBeenCalledWith('league');
        expect(result).toMatchObject(expectedResponse);
        expect(result.data).toHaveLength(1);
        expect(cacheService.setCache).toHaveBeenCalledWith(
            'leagues_list_2025_England_all_1_1',
            JSON.stringify(expectedResponse),
            604800,
        );
    });

    it('should fetch leagues from DB case-insensitive', async () => {
        const query: GetLeaguesDto = { season: 2024, country: 'vietnam', page: 1, limit: 1 };
        const leagues = [
            new League(),
        ];
        Object.assign(leagues[0], {
            id: 1,
            externalId: 322,
            name: 'V League 1',
            country: 'vietnam',
            logo: 'https://example.com/leagues/322.png',
            flag: 'https://example.com/vn.svg',
            season: 2024,
            active: true,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        const expectedResponse: PaginatedLeaguesResponse = {
            data: [
                {
                    id: 1,
                    externalId: 322,
                    name: 'V League 1',
                    country: 'vietnam',
                    logo: 'https://example.com/leagues/322.png',
                    flag: 'https://example.com/vn.svg',
                    season: 2024,
                    type: 'league',
                    active: true,
                },
            ],
            meta: {
                totalItems: 1,
                totalPages: 1,
                currentPage: 1,
                limit: 1,
            },
            status: 200,
        };
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(leagueRepository, 'createQueryBuilder').mockReturnValue({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([leagues, 1]),
        } as any);
        jest.spyOn(cacheService, 'setCache').mockResolvedValueOnce();

        const result = await service.getLeagues(query);

        expect(leagueRepository.createQueryBuilder).toHaveBeenCalledWith('league');
        expect(leagueRepository.createQueryBuilder().andWhere).toHaveBeenCalledWith(
            'league.country = :country',
            { country: 'vietnam' },
        );
        expect(result).toMatchObject(expectedResponse);
        expect(result.data).toHaveLength(1);
    });

    it('should fetch leagues from DB with active filter', async () => {
        const query: GetLeaguesDto = { season: 2024, country: 'vietnam', active: true, page: 1, limit: 1 };
        const leagues = [
            new League(),
        ];
        Object.assign(leagues[0], {
            id: 1,
            externalId: 322,
            name: 'V League 1',
            country: 'vietnam',
            logo: 'https://example.com/leagues/322.png',
            flag: 'https://example.com/vn.svg',
            season: 2024,
            active: true,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        const expectedResponse: PaginatedLeaguesResponse = {
            data: [
                {
                    id: 1,
                    externalId: 322,
                    name: 'V League 1',
                    country: 'vietnam',
                    logo: 'https://example.com/leagues/322.png',
                    flag: 'https://example.com/vn.svg',
                    season: 2024,
                    type: 'league',
                    active: true,
                },
            ],
            meta: {
                totalItems: 1,
                totalPages: 1,
                currentPage: 1,
                limit: 1,
            },
            status: 200,
        };
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(leagueRepository, 'createQueryBuilder').mockReturnValue({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([leagues, 1]),
        } as any);
        jest.spyOn(cacheService, 'setCache').mockResolvedValueOnce();

        const result = await service.getLeagues(query);

        expect(leagueRepository.createQueryBuilder).toHaveBeenCalledWith('league');
        expect(leagueRepository.createQueryBuilder().andWhere).toHaveBeenCalledWith(
            'league.country = :country',
            { country: 'vietnam' },
        );
        expect(leagueRepository.createQueryBuilder().andWhere).toHaveBeenCalledWith(
            'league.active = :active',
            { active: true },
        );
        expect(result).toMatchObject(expectedResponse);
        expect(result.data).toHaveLength(1);
    });

    it('should fetch leagues from API with newdb=true, bypassing cache and DB', async () => {
        const query: GetLeaguesDto = { season: 2024, country: 'Vietnam', page: 1, limit: 1, newdb: true };
        const apiData = [
            {
                league: {
                    id: 322,
                    name: 'V League 1',
                    logo: 'https://media.api-sports.io/football/leagues/322.png',
                    flag: 'https://media.api-sports.io/flags/vietnam.svg',
                },
                country: { name: 'Vietnam' },
                seasons: [{ year: 2024 }],
            },
        ];
        const savedLeague = new League();
        Object.assign(savedLeague, {
            id: 1,
            externalId: 322,
            name: 'V League 1',
            country: 'vietnam',
            logo: 'https://example.com/leagues/322.png',
            flag: 'https://example.com/vn.svg',
            season: 2024,
            active: true,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        const expectedResponse: PaginatedLeaguesResponse = {
            data: [
                {
                    id: 1,
                    externalId: 322,
                    name: 'V League 1',
                    country: 'vietnam',
                    logo: 'https://example.com/leagues/322.png',
                    flag: 'https://example.com/vn.svg',
                    season: 2024,
                    type: 'league',
                    active: true,
                },
            ],
            meta: {
                totalItems: 1,
                totalPages: 1,
                currentPage: 1,
                limit: 1,
            },
            status: 200,
        };
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(leagueRepository, 'createQueryBuilder').mockReturnValue({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([[savedLeague], 1]),
        } as any);
        jest.spyOn(leagueRepository, 'findOneBy').mockResolvedValueOnce(null);
        jest.spyOn(axios, 'get').mockResolvedValueOnce({ data: { response: apiData } });
        jest.spyOn(imageService, 'downloadImage').mockImplementation((url: string) => {
            if (url.includes('leagues/322.png')) return Promise.resolve('https://example.com/leagues/322.png');
            if (url.includes('flags/vietnam.svg')) return Promise.resolve('https://example.com/vn.svg');
            return Promise.resolve('');
        });
        jest.spyOn(leagueRepository, 'create').mockReturnValueOnce(savedLeague);
        jest.spyOn(leagueRepository, 'save').mockResolvedValueOnce(savedLeague);
        jest.spyOn(cacheService, 'deleteByPattern').mockResolvedValueOnce();
        jest.spyOn(cacheService, 'setCache').mockResolvedValueOnce();

        const result = await service.getLeagues(query);

        expect(cacheService.getCache).not.toHaveBeenCalled();
        expect(axios.get).toHaveBeenCalledWith(
            'https://v3.football.api-sports.io/leagues',
            expect.objectContaining({
                params: { season: 2024, country: 'Vietnam' },
            }),
        );
        expect(leagueRepository.save).toHaveBeenCalledWith([savedLeague]);
        expect(leagueRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
        expect(cacheService.deleteByPattern).toHaveBeenCalledWith('leagues_list_*_2024_vietnam_*');
        expect(cacheService.setCache).toHaveBeenCalledWith(
            'leagues_list_2024_Vietnam_all_1_1',
            JSON.stringify(expectedResponse),
            604800,
        );
        expect(result).toMatchObject(expectedResponse);
        expect(result.data).toHaveLength(1);
    });

    it('should fetch leagues from API and save to DB with active true', async () => {
        const query: GetLeaguesDto = { season: 2024, country: 'Vietnam', page: 1, limit: 1 };
        const apiData = [
            {
                league: {
                    id: 322,
                    name: 'V League 1',
                    logo: 'https://media.api-sports.io/football/leagues/322.png',
                    flag: 'https://media.api-sports.io/flags/vietnam.svg',
                },
                country: { name: 'Vietnam' },
                seasons: [{ year: 2024 }],
            },
        ];
        const savedLeague = new League();
        Object.assign(savedLeague, {
            id: 1,
            externalId: 322,
            name: 'V League 1',
            country: 'vietnam',
            logo: 'https://example.com/leagues/322.png',
            flag: 'https://example.com/vn.svg',
            season: 2024,
            active: true,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        const expectedResponse: PaginatedLeaguesResponse = {
            data: [
                {
                    id: 1,
                    externalId: 322,
                    name: 'V League 1',
                    country: 'vietnam',
                    logo: 'https://example.com/leagues/322.png',
                    flag: 'https://example.com/vn.svg',
                    season: 2024,
                    type: 'league',
                    active: true,
                },
            ],
            meta: {
                totalItems: 1,
                totalPages: 1,
                currentPage: 1,
                limit: 1,
            },
            status: 200,
        };
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        const mockQueryBuilder = {
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn(),
        };
        let callCount = 0;
        jest.spyOn(leagueRepository, 'createQueryBuilder').mockImplementation(() => {
            callCount++;
            if (callCount === 1) {
                mockQueryBuilder.getManyAndCount.mockResolvedValueOnce([[], 0]);
            } else {
                mockQueryBuilder.getManyAndCount.mockResolvedValueOnce([[savedLeague], 1]);
            }
            return mockQueryBuilder as any;
        });
        jest.spyOn(leagueRepository, 'findOneBy').mockResolvedValueOnce(null);
        jest.spyOn(axios, 'get').mockResolvedValueOnce({ data: { response: apiData } });
        jest.spyOn(imageService, 'downloadImage').mockImplementation((url: string) => {
            if (url.includes('leagues/322.png')) return Promise.resolve('https://example.com/leagues/322.png');
            if (url.includes('flags/vietnam.svg')) return Promise.resolve('https://example.com/vn.svg');
            return Promise.resolve('');
        });
        jest.spyOn(leagueRepository, 'create').mockReturnValueOnce(savedLeague);
        jest.spyOn(leagueRepository, 'save').mockResolvedValueOnce(savedLeague);
        jest.spyOn(cacheService, 'setCache').mockResolvedValueOnce();

        const result = await service.getLeagues(query);

        expect(axios.get).toHaveBeenCalledWith(
            'https://v3.football.api-sports.io/leagues',
            expect.objectContaining({
                params: { season: 2024, country: 'Vietnam' },
            }),
        );
        expect(leagueRepository.createQueryBuilder).toHaveBeenCalledTimes(2);
        expect(leagueRepository.save).toHaveBeenCalledWith([savedLeague]);
        expect(result).toMatchObject(expectedResponse);
        expect(result.data).toHaveLength(1);
        expect(cacheService.setCache).toHaveBeenCalledWith(
            'leagues_list_2024_Vietnam_all_1_1',
            JSON.stringify(expectedResponse),
            604800,
        );
    });

    it('should handle API failure gracefully', async () => {
        const query: GetLeaguesDto = { season: 2024, country: 'Vietnam', page: 1, limit: 1 };
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(leagueRepository, 'createQueryBuilder').mockReturnValue({
            andWhere: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            take: jest.fn().mockReturnThis(),
            getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
        } as any);
        jest.spyOn(axios, 'get').mockRejectedValue(new Error('Network error'));

        const result = await service.getLeagues(query);

        expect(axios.get).toHaveBeenCalledTimes(3);
        expect(result).toEqual({
            data: [],
            meta: {
                totalItems: 0,
                totalPages: 0,
                currentPage: 1,
                limit: 1,
            },
            status: 200,
        });
    });
});