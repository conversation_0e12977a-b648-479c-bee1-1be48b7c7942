import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { League } from '../../../src/sports/football/models/league.entity';
import { Fixture } from '../../../src/sports/football/models/fixture.entity';

describe('League Entity', () => {
  let leagueRepository: Repository<League>;
  let fixtureRepository: Repository<Fixture>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: getRepositoryToken(League),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Fixture),
          useClass: Repository,
        },
      ],
    }).compile();

    leagueRepository = module.get<Repository<League>>(getRepositoryToken(League));
    fixtureRepository = module.get<Repository<Fixture>>(getRepositoryToken(Fixture));
  });

  it('should create a league with associated fixture', async () => {
    const leagueData: Partial<League> = {
      externalId: 850,
      name: 'UEFA U21 Championship - Qualification',
      country: 'World',
      logo: '/public/images/leagues/850.png',
      flag: '',
      season: 2025,
    };

    const fixtureData: Partial<Fixture> = {
      externalId: 1354626,
      leagueId: 850,
      leagueName: 'UEFA U21 Championship - Qualification',
      season: 2025,
      round: 'Regular Season - 9',
      homeTeamId: 33,
      awayTeamId: 40,
      slug: 'manchester-united-vs-chelsea-2023-10-20',
      date: new Date('2023-10-20T14:00:00Z'),
      venueId: 465,
      venueName: 'Estadio Olímpico Atahualpa',
      venueCity: 'Quito',
      referee: 'John Doe',
      source: 'api',
      createdBy: null,
      timestamp: 1697810400,
      data: {
        homeTeamName: 'Manchester United',
        homeTeamLogo: '/public/images/teams/33.png',
        awayTeamName: 'Chelsea',
        awayTeamLogo: '/public/images/teams/40.png',
        status: 'FT',
        statusLong: 'Match Finished', // Thêm
        statusExtra: 5, // Thêm
        elapsed: 90,
        goalsHome: 2,
        goalsAway: 1,
        scoreHalftimeHome: 1,
        scoreHalftimeAway: 0,
        scoreFulltimeHome: 2,
        scoreFulltimeAway: 1,
        periods: {
          first: 1747267200,
          second: 1747270800,
        },
      },
    };

    jest.spyOn(leagueRepository, 'create').mockReturnValue(leagueData as League);
    jest.spyOn(leagueRepository, 'save').mockResolvedValue(leagueData as League);
    jest.spyOn(fixtureRepository, 'create').mockReturnValue(fixtureData as Fixture);
    jest.spyOn(fixtureRepository, 'save').mockResolvedValue(fixtureData as Fixture);

    const league = leagueRepository.create(leagueData);
    const savedLeague = await leagueRepository.save(league);

    const fixture = fixtureRepository.create(fixtureData);
    const savedFixture = await fixtureRepository.save(fixture);

    expect(league).toEqual(leagueData);
    expect(savedLeague).toEqual(leagueData);
    expect(savedFixture.data).toMatchObject({
      status: 'FT',
      statusLong: 'Match Finished',
      statusExtra: 5,
    });
  });

  // Các test case khác (nếu có) cũng cần cập nhật tương tự
});