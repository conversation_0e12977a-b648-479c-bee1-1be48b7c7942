import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BroadcastLinkService } from '../../src/broadcast-links/broadcast-link.service';
import { BroadcastLink } from '../../src/broadcast-links/broadcast-link.entity';
import { Fixture } from '../../src/sports/football/models/fixture.entity';
import { CreateBroadcastLinkDto, UpdateBroadcastLinkDto } from '../../src/broadcast-links/broadcast-link.dto';

describe('BroadcastLinkService', () => {
    let service: BroadcastLinkService;
    let broadcastLinkRepository: Repository<BroadcastLink>;
    let fixtureRepository: Repository<Fixture>;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                BroadcastLinkService,
                {
                    provide: getRepositoryToken(BroadcastLink),
                    useClass: Repository,
                },
                {
                    provide: getRepositoryToken(Fixture),
                    useClass: Repository,
                },
            ],
        }).compile();

        service = module.get<BroadcastLinkService>(BroadcastLinkService);
        broadcastLinkRepository = module.get<Repository<BroadcastLink>>(getRepositoryToken(BroadcastLink));
        fixtureRepository = module.get<Repository<Fixture>>(getRepositoryToken(Fixture));
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should create a broadcast link', async () => {
        const createBroadcastLinkDto: CreateBroadcastLinkDto = {
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
        };
        const fixture = { id: 1, externalId: 1354626 } as Fixture;
        const savedBroadcastLink = new BroadcastLink();
        Object.assign(savedBroadcastLink, {
            id: 1,
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(fixture);
        jest.spyOn(broadcastLinkRepository, 'save').mockResolvedValueOnce(savedBroadcastLink);

        const result = await service.createBroadcastLink(createBroadcastLinkDto);

        expect(fixtureRepository.findOneBy).toHaveBeenCalledWith({ externalId: 1354626 });
        expect(broadcastLinkRepository.save).toHaveBeenCalledWith(expect.any(BroadcastLink));
        expect(result).toMatchObject({
            id: 1,
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
        });
    });

    it('should throw error if fixture not found when creating', async () => {
        const createBroadcastLinkDto: CreateBroadcastLinkDto = {
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
        };
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(null);

        await expect(service.createBroadcastLink(createBroadcastLinkDto)).rejects.toThrow(
            `Fixture with externalId ${createBroadcastLinkDto.fixtureId} not found`,
        );
    });

    it('should throw error if linkUrl is invalid when creating', async () => {
        const createBroadcastLinkDto: CreateBroadcastLinkDto = {
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'invalid-url',
            addedBy: 1,
        };
        const fixture = { id: 1, externalId: 1354626 } as Fixture;
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(fixture);

        await expect(service.createBroadcastLink(createBroadcastLinkDto)).rejects.toThrow(
            `Invalid URL: ${createBroadcastLinkDto.linkUrl}`,
        );
    });

    it('should update a broadcast link', async () => {
        const id = 1;
        const updateBroadcastLinkDto: UpdateBroadcastLinkDto = {
            linkName: 'Updated YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=xyz',
            addedBy: 2,
        };
        const existingBroadcastLink = new BroadcastLink();
        Object.assign(existingBroadcastLink, {
            id: 1,
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        const updatedBroadcastLink = new BroadcastLink();
        Object.assign(updatedBroadcastLink, {
            ...existingBroadcastLink,
            linkName: 'Updated YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=xyz',
            addedBy: 2,
            updatedAt: new Date('2025-05-14T11:00:00Z'),
        });
        jest.spyOn(broadcastLinkRepository, 'findOneBy').mockResolvedValueOnce(existingBroadcastLink);
        jest.spyOn(broadcastLinkRepository, 'save').mockResolvedValueOnce(updatedBroadcastLink);

        const result = await service.updateBroadcastLink(id, updateBroadcastLinkDto);

        expect(broadcastLinkRepository.findOneBy).toHaveBeenCalledWith({ id });
        expect(broadcastLinkRepository.save).toHaveBeenCalledWith(expect.any(BroadcastLink));
        expect(result).toMatchObject({
            id: 1,
            fixtureId: 1354626,
            linkName: 'Updated YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=xyz',
            addedBy: 2,
        });
    });

    it('should throw error if broadcast link not found when updating', async () => {
        const id = 1;
        const updateBroadcastLinkDto: UpdateBroadcastLinkDto = {
            linkName: 'Updated YouTube Stream',
        };
        jest.spyOn(broadcastLinkRepository, 'findOneBy').mockResolvedValueOnce(null);

        await expect(service.updateBroadcastLink(id, updateBroadcastLinkDto)).rejects.toThrow(
            `Broadcast link with id ${id} not found`,
        );
    });

    it('should throw error if linkUrl is invalid when updating', async () => {
        const id = 1;
        const updateBroadcastLinkDto: UpdateBroadcastLinkDto = {
            linkUrl: 'invalid-url',
        };
        const existingBroadcastLink = new BroadcastLink();
        Object.assign(existingBroadcastLink, {
            id: 1,
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        jest.spyOn(broadcastLinkRepository, 'findOneBy').mockResolvedValueOnce(existingBroadcastLink);

        await expect(service.updateBroadcastLink(id, updateBroadcastLinkDto)).rejects.toThrow(
            `Invalid URL: ${updateBroadcastLinkDto.linkUrl}`,
        );
    });

    it('should delete a broadcast link', async () => {
        const id = 1;
        const existingBroadcastLink = new BroadcastLink();
        Object.assign(existingBroadcastLink, {
            id: 1,
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        jest.spyOn(broadcastLinkRepository, 'findOneBy').mockResolvedValueOnce(existingBroadcastLink);
        jest.spyOn(broadcastLinkRepository, 'delete').mockResolvedValueOnce({ affected: 1 } as any);

        await service.deleteBroadcastLink(id);

        expect(broadcastLinkRepository.findOneBy).toHaveBeenCalledWith({ id });
        expect(broadcastLinkRepository.delete).toHaveBeenCalledWith(id);
    });

    it('should throw error if broadcast link not found when deleting', async () => {
        const id = 1;
        jest.spyOn(broadcastLinkRepository, 'findOneBy').mockResolvedValueOnce(null);

        await expect(service.deleteBroadcastLink(id)).rejects.toThrow(
            `Broadcast link with id ${id} not found`,
        );
    });

    it('should get broadcast links by fixtureId', async () => {
        const fixtureId = 1354626;
        const fixture = { id: 1, externalId: 1354626 } as Fixture;
        const broadcastLinks = [
            new BroadcastLink(),
            new BroadcastLink(),
        ];
        Object.assign(broadcastLinks[0], {
            id: 1,
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
            createdAt: new Date('2025-05-14T10:00:00Z'),
            updatedAt: new Date('2025-05-14T10:00:00Z'),
        });
        Object.assign(broadcastLinks[1], {
            id: 2,
            fixtureId: 1354626,
            linkName: 'Twitch Live',
            linkUrl: 'https://twitch.tv/stream',
            addedBy: 2,
            createdAt: new Date('2025-05-14T11:00:00Z'),
            updatedAt: new Date('2025-05-14T11:00:00Z'),
        });
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(fixture);
        jest.spyOn(broadcastLinkRepository, 'find').mockResolvedValueOnce(broadcastLinks);

        const result = await service.getBroadcastLinksByFixtureId(fixtureId);

        expect(fixtureRepository.findOneBy).toHaveBeenCalledWith({ externalId: fixtureId });
        expect(broadcastLinkRepository.find).toHaveBeenCalledWith({ where: { fixtureId } });
        expect(result).toHaveLength(2);
        expect(result[0]).toMatchObject({
            id: 1,
            fixtureId: 1354626,
            linkName: 'YouTube Stream',
            linkUrl: 'https://youtube.com/watch?v=abc',
            addedBy: 1,
        });
    });

    it('should throw error if fixture not found when getting links', async () => {
        const fixtureId = 1354626;
        jest.spyOn(fixtureRepository, 'findOneBy').mockResolvedValueOnce(null);

        await expect(service.getBroadcastLinksByFixtureId(fixtureId)).rejects.toThrow(
            `Fixture with externalId ${fixtureId} not found`,
        );
    });
});