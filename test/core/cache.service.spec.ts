import { Test, TestingModule } from '@nestjs/testing';
import { CacheService } from '../../src/core/cache/cache.service';
import { ConfigModule } from '@nestjs/config';
import configuration from '../../src/core/config/configuration';
import * as Redis from 'ioredis';

// Mock ioredis như một class
jest.mock('ioredis', () => {
    return {
        Redis: jest.fn().mockImplementation(() => ({
            set: jest.fn().mockResolvedValue('OK'),
            get: jest.fn().mockResolvedValue(null),
        })),
    };
});

describe('CacheService', () => {
    let service: CacheService;
    let redisMock: jest.Mocked<Redis.Redis>;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            imports: [ConfigModule.forRoot({ load: [configuration] })],
            providers: [CacheService],
        }).compile();

        service = module.get<CacheService>(CacheService);
        redisMock = (service as any).redis; // Truy cập private redis
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should set and get cache', async () => {
        const key = 'test_key';
        const value = 'test_value';
        redisMock.get.mockResolvedValueOnce(value); // Mock get trả về value

        await service.setCache(key, value, 60);
        const result = await service.getCache(key);

        expect(redisMock.set).toHaveBeenCalledWith(key, value, 'EX', 60);
        expect(result).toBe(value);
    });

    it('should return null for non-existent key', async () => {
        const result = await service.getCache('non_existent_key');
        expect(result).toBeNull();
    });
});