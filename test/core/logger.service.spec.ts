import { Test, TestingModule } from '@nestjs/testing';
import { LoggerService } from '../../src/core/logger/logger.service';

describe('LoggerService', () => {
  let service: LoggerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LoggerService],
    }).compile();

    service = module.get<LoggerService>(LoggerService);
  });

  it('should log error', () => {
    const spy = jest.spyOn(LoggerService.prototype, 'error');
    service.error('Mock error for testing', 'stack trace', 'TestContext');
    expect(spy).toHaveBeenCalledWith('Mock error for testing', 'stack trace', 'TestContext');
  });

  it('should log info', () => {
    const spy = jest.spyOn(LoggerService.prototype, 'log');
    service.log('Mock info for testing', 'TestContext');
    expect(spy).toHaveBeenCalledWith('Mock info for testing', 'TestContext');
  });
});