import { Test, TestingModule } from '@nestjs/testing';
import { UtilsService } from '../../src/shared/services/utils.service';

describe('UtilsService', () => {
    let service: UtilsService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [UtilsService],
        }).compile();

        service = module.get<UtilsService>(UtilsService);
    });

    it('should generate slug', () => {
        const input = 'Manchester United vs Chelsea';
        const date = '2023-10-20';
        const slug = service.generateSlug(input, date);
        expect(slug).toBe('manchester-united-vs-chelsea-2023-10-20');
    });

    it('should format date', () => {
        const date = new Date('2023-10-20T14:00:00Z');
        const result = service.formatDate(date);
        expect(result).toBe('2023-10-20');
    });
});