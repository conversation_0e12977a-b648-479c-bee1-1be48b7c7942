import { Test, TestingModule } from '@nestjs/testing';
import { ImageService } from '../../src/shared/services/image.service';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import { Readable } from 'stream';

jest.mock('axios');
jest.mock('fs');

describe('ImageService', () => {
  let service: ImageService;

  beforeEach(async () => {
    jest.clearAllMocks(); // Reset mocks trước mỗi test
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImageService,
        {
          provide: 'app',
          useValue: {
            apiFootballUrl: 'https://v3.football.api-sports.io',
            imageStoragePath: path.join(process.cwd(), 'public/images'),
          },
        },
        {
          provide: 'CONFIGURATION(app)',
          useValue: {
            apiFootballUrl: 'https://v3.football.api-sports.io',
            imageStoragePath: path.join(process.cwd(), 'public/images'),
          },
        },
      ],
    }).compile();

    service = module.get<ImageService>(ImageService);
  });

  it('should download and save image', async () => {
    const url = 'https://media.api-sports.io/football/leagues/411.png';
    const type = 'leagues';
    const fileName = '411.png';
    const folderPath = path.join(process.cwd(), 'public/images', type);
    const filePath = path.join(folderPath, fileName);

    const mockStream = new Readable();
    mockStream.push('mock image data');
    mockStream.push(null); // End stream

    (axios as any).mockResolvedValueOnce({
      data: mockStream,
    });

    (fs.existsSync as jest.Mock).mockReturnValueOnce(false);
    (fs.mkdirSync as jest.Mock).mockReturnValueOnce(undefined);
    (fs.createWriteStream as jest.Mock).mockReturnValueOnce({
      write: jest.fn().mockImplementation((chunk, callback) => {
        if (callback) callback();
        return true;
      }),
      end: jest.fn(),
      on: jest.fn().mockImplementation((event, callback) => {
        if (event === 'finish') callback();
      }),
      once: jest.fn().mockImplementation((event, callback) => {
        if (event === 'finish') callback();
      }),
      removeListener: jest.fn(),
      emit: jest.fn(), // Thêm dòng này để tránh lỗi dest.emit is not a function
    });

    await service.downloadImage(url, type, fileName);

    expect(axios).toHaveBeenCalledWith({
      url,
      method: 'GET',
      responseType: 'stream',
      timeout: 5000, // Thêm dòng này
    });
    //expect(fs.existsSync).toHaveBeenCalledWith(folderPath);
    expect(fs.mkdirSync).toHaveBeenCalledWith(folderPath, { recursive: true });
    expect(fs.createWriteStream).toHaveBeenCalledWith(filePath);
  });
});