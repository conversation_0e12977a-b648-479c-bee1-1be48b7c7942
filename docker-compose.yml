version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: sports_game
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - sports-network

  # Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - sports-network

  # API Service
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_NAME=sports_game
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - postgres
      - redis
    networks:
      - sports-network
    restart: unless-stopped

  # Worker Service
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_NAME=sports_game
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SYNC_BATCH_SIZE=100
      - MAX_IDS_PER_REQUEST=20
      - MAX_CONCURRENT_REQUESTS=1
    depends_on:
      - postgres
      - redis
    networks:
      - sports-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  sports-network:
    driver: bridge
