# Image Upload Endpoints Implementation

## 🎯 **Overview**

Implemented comprehensive image upload system with two upload methods (file and URL) exclusively for SystemUser roles (Admin/Moderator/Editor).

## ✅ **Features Implemented**

### **1. Dual Upload Methods:**
- **File Upload**: Direct file upload via multipart/form-data
- **URL Upload**: Download and store images from remote URLs

### **2. Authentication & Authorization:**
- **SystemUser Only**: Admin, Moderator, Editor roles
- **JWT Authentication**: Bearer token required
- **Role-based Access**: All SystemUser roles can upload

### **3. File Management:**
- **Category Organization**: leagues, teams, flags, venues, general
- **File Validation**: PNG, JPG, JPEG, GIF, SVG only
- **Size Limit**: 10MB maximum
- **Unique Naming**: Timestamp-based filename generation
- **Storage**: Local filesystem with public URL access

### **4. Database Tracking:**
- **Complete Metadata**: File info, upload tracking, user attribution
- **Search & Filter**: Category filtering, filename/description search
- **Pagination**: Efficient data retrieval

## 🔧 **Technical Implementation**

### **A. Database Entity:**
```typescript
@Entity('uploaded_images')
export class UploadedImage {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true })
    imageId: string; // 'img_a1b2c3d4e5f6g7h8'

    @Column()
    originalName: string;

    @Column()
    filename: string; // 'leagues/premier-league-logo-1640995200000.png'

    @Column()
    size: number;

    @Column()
    mimeType: string;

    @Column({ type: 'enum', enum: ImageCategory })
    category: ImageCategory;

    @Column()
    path: string; // Local file path

    @Column()
    url: string; // Public URL

    @Column({ nullable: true })
    description?: string;

    @Column()
    uploadedBy: number; // SystemUser ID

    @Column({ nullable: true })
    sourceUrl?: string; // Original URL if uploaded from URL

    @CreateDateColumn()
    uploadedAt: Date;
}
```

### **B. Service Implementation:**
```typescript
@Injectable()
export class UploadService {
    // File upload from multipart data
    async uploadImageFromFile(
        file: Express.Multer.File,
        category: ImageCategory,
        uploadedBy: number,
        description?: string
    ): Promise<UploadImageResponseDto>

    // Image upload from remote URL
    async uploadImageFromUrl(
        uploadDto: UploadImageByUrlDto,
        uploadedBy: number
    ): Promise<UploadImageResponseDto>

    // Get images with pagination and filtering
    async getImages(query: GetImagesDto): Promise<ImageListResponseDto>

    // Get specific image by ID
    async getImageById(imageId: string): Promise<UploadImageResponseDto>

    // Delete image (file + database record)
    async deleteImage(imageId: string, userId: number): Promise<void>
}
```

### **C. Controller Endpoints:**
```typescript
@ApiTags('Image Upload')
@ApiBearerAuth('bearer')
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
@Controller('upload')
export class UploadController {
    // POST /upload/file - Upload from file
    // POST /upload/url - Upload from URL
    // GET /upload - List images with pagination
    // GET /upload/:imageId - Get specific image
    // DELETE /upload/:imageId - Delete image
}
```

## 📊 **API Endpoints**

### **1. POST /upload/file - Upload Image from File**
```typescript
**Authentication:** SystemUser (Admin/Moderator/Editor)
**Content-Type:** multipart/form-data

**Request:**
- file: Binary image file
- category: ImageCategory enum
- description: Optional description

**Response:**
{
  "id": "img_a1b2c3d4e5f6g7h8",
  "originalName": "premier-league-logo.png",
  "filename": "leagues/premier-league-logo-1640995200000.png",
  "size": 15420,
  "mimeType": "image/png",
  "category": "leagues",
  "url": "http://localhost:3000/uploads/leagues/premier-league-logo-1640995200000.png",
  "path": "./public/images/leagues/premier-league-logo-1640995200000.png",
  "uploadedAt": "2025-05-25T10:30:00.000Z",
  "uploadedBy": 1,
  "description": "Premier League official logo"
}
```

### **2. POST /upload/url - Upload Image from URL**
```typescript
**Authentication:** SystemUser (Admin/Moderator/Editor)
**Content-Type:** application/json

**Request:**
{
  "imageUrl": "https://media.api-sports.io/football/leagues/39.png",
  "category": "leagues",
  "filename": "premier-league-logo",
  "description": "Premier League official logo"
}

**Response:** Same as file upload
```

### **3. GET /upload - List Images**
```typescript
**Authentication:** SystemUser (Admin/Moderator/Editor)

**Query Parameters:**
- page: Page number (default: 1)
- limit: Items per page (default: 20)
- category: Filter by ImageCategory
- search: Search in filename or description

**Response:**
{
  "data": [UploadImageResponseDto[]],
  "meta": {
    "totalItems": 150,
    "totalPages": 8,
    "currentPage": 1,
    "limit": 20
  }
}
```

### **4. GET /upload/:imageId - Get Image Details**
```typescript
**Authentication:** SystemUser (Admin/Moderator/Editor)

**Response:** UploadImageResponseDto
```

### **5. DELETE /upload/:imageId - Delete Image**
```typescript
**Authentication:** SystemUser (Admin/Moderator/Editor)

**Response:** 204 No Content
**Action:** Deletes both file and database record
```

## 🔒 **Security Features**

### **A. Authentication:**
- **JWT Bearer Token**: Required for all endpoints
- **SystemUser Only**: Admin, Moderator, Editor roles
- **User Attribution**: All uploads tracked by user ID

### **B. File Validation:**
- **MIME Type Check**: Only image types allowed
- **File Size Limit**: 10MB maximum
- **Extension Validation**: PNG, JPG, JPEG, GIF, SVG only
- **Filename Sanitization**: Safe filename generation

### **C. URL Upload Security:**
- **URL Validation**: Valid URL format required
- **Download Timeout**: 30 seconds maximum
- **Size Limit**: 10MB maximum download
- **Content-Type Check**: Validates image type from headers

## 🗂️ **File Organization**

### **A. Category Structure:**
```
./public/images/
├── leagues/          # League logos
├── teams/            # Team logos  
├── flags/            # Country flags
├── venues/           # Stadium images
└── general/          # General purpose images
```

### **B. Filename Convention:**
```
{sanitized-name}-{timestamp}.{extension}
Examples:
- premier-league-logo-1640995200000.png
- manchester-united-logo-1640995300000.jpg
- england-flag-1640995400000.svg
```

### **C. Public URL Access:**
```
http://localhost:3000/uploads/{category}/{filename}
Examples:
- http://localhost:3000/uploads/leagues/premier-league-logo-1640995200000.png
- http://localhost:3000/uploads/teams/manchester-united-logo-1640995300000.jpg
```

## 🧪 **Testing Guide**

### **Step 1: Authentication**
```bash
# Login to get token
curl -X POST http://localhost:3000/system-auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123456"}'

# Copy accessToken from response
```

### **Step 2: Upload File**
```bash
# Upload image file
curl -X POST http://localhost:3000/upload/file \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "file=@/path/to/image.png" \
  -F "category=leagues" \
  -F "description=Test league logo"
```

### **Step 3: Upload from URL**
```bash
# Upload from URL
curl -X POST http://localhost:3000/upload/url \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://media.api-sports.io/football/leagues/39.png",
    "category": "leagues",
    "filename": "premier-league-logo",
    "description": "Premier League official logo"
  }'
```

### **Step 4: List Images**
```bash
# Get uploaded images
curl -X GET "http://localhost:3000/upload?category=leagues&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### **Step 5: Access Uploaded Image**
```bash
# Access via public URL (from response)
http://localhost:3000/uploads/leagues/premier-league-logo-1640995200000.png
```

## 📈 **Benefits**

### **✅ Comprehensive Upload System:**
- **Dual Methods**: File upload and URL download
- **Category Organization**: Structured file management
- **Metadata Tracking**: Complete upload history
- **Public Access**: Direct URL access to images

### **✅ Security & Validation:**
- **Role-based Access**: SystemUser only
- **File Validation**: Type and size checking
- **Safe Storage**: Sanitized filenames
- **Audit Trail**: User attribution and timestamps

### **✅ Developer Experience:**
- **Swagger Documentation**: Complete API documentation
- **Error Handling**: Clear error messages
- **Pagination**: Efficient data retrieval
- **Search & Filter**: Easy image management

### **✅ Production Ready:**
- **Static File Serving**: Configured in main.ts
- **Database Integration**: Complete entity management
- **Error Recovery**: Graceful error handling
- **Scalable Architecture**: Modular design

---

**Implementation Completed:** 2025-05-25
**Status:** ✅ Complete image upload system with dual methods
**Authentication:** ✅ SystemUser only (Admin/Moderator/Editor)
**Build Status:** ✅ npm run build successful
**Ready for Testing:** ✅ All endpoints documented and functional
