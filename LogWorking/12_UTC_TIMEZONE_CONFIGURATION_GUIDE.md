# UTC Timezone Configuration Guide

## 🎯 **Vấn đề Timezone trong Production**

Ứng dụng cần đảm bảo tất cả thời gian đều sử dụng UTC để tránh inconsistency khi deploy trên các môi trường khác nhau.

### **Potential Issues:**
- **System timezone** khác nhau giữa dev/staging/production
- **Database timezone** settings
- **JavaScript Date objects** bị ảnh hưởng bởi system timezone
- **Cronjob scheduling** không chính xác

## ✅ **Current UTC Configuration**

### **1. Application Scripts (package.json):**
```json
{
  "start:api:dev": "TZ=UTC nest start --watch",
  "start:worker:dev": "TZ=UTC nest start --entryFile worker --watch",
  "start:api:prod": "node dist/main",
  "start:worker:prod": "node dist/worker"
}
```

### **2. Cronjob Configuration:**
```typescript
@Cron(CronExpression.EVERY_10_SECONDS, { utcOffset: 0 })
@Cron('0 2 * * *', { utcOffset: 0 })
```

### **3. Database Schema:**
```typescript
@Column({ type: 'timestamp with time zone' })
date: Date;

@CreateDateColumn({ type: 'timestamp with time zone' })
createdAt: Date;

@UpdateDateColumn({ type: 'timestamp with time zone' })
updatedAt: Date;
```

### **4. UTC Helper Functions (UtilsService):**
```typescript
getUtcNow(): Date                                    // Current UTC time
parseUtcDate(dateString: string): Date              // Parse to UTC
getUtcTimestamp(): number                           // UTC timestamp
getMinutesDifference(futureDate: Date): number      // UTC-safe time diff
```

## 🔧 **Production Deployment Setup**

### **1. Environment Variables:**

#### **For Docker:**
```dockerfile
# Dockerfile
ENV TZ=UTC
ENV NODE_TZ=UTC
```

#### **For PM2 Ecosystem:**
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'api-sports-game',
      script: 'dist/main.js',
      env: {
        TZ: 'UTC',
        NODE_TZ: 'UTC'
      }
    },
    {
      name: 'auto-update-sports-game',
      script: 'dist/worker.js',
      env: {
        TZ: 'UTC',
        NODE_TZ: 'UTC'
      }
    }
  ]
};
```

#### **For System Service:**
```bash
# /etc/systemd/system/api-sports-game.service
[Unit]
Description=API Sports Game

[Service]
Environment=TZ=UTC
Environment=NODE_TZ=UTC
ExecStart=/usr/bin/node /path/to/dist/main.js
```

### **2. Database Configuration:**

#### **PostgreSQL:**
```sql
-- Check current timezone
SHOW timezone;

-- Set to UTC (if needed)
ALTER DATABASE your_database SET timezone TO 'UTC';

-- Or in postgresql.conf
timezone = 'UTC'
```

#### **Connection String:**
```bash
# .env
DATABASE_URL=********************************/db?timezone=UTC
```

### **3. System Level (Optional):**

#### **Ubuntu/Debian:**
```bash
# Set system timezone to UTC
sudo timedatectl set-timezone UTC

# Verify
timedatectl status
```

#### **Docker Container:**
```dockerfile
# Set timezone in container
RUN ln -snf /usr/share/zoneinfo/UTC /etc/localtime && echo UTC > /etc/timezone
```

## 🧪 **Testing UTC Configuration**

### **1. Automatic Timezone Check:**
Application automatically checks timezone configuration on startup:

```
[DatabaseService] Database timezone: UTC
[DatabaseService] ✅ Database timezone is correctly set to UTC
[DatabaseService] ✅ Application timezone is correctly set to UTC
```

### **2. Manual Test Script:**
```bash
# Test UTC configuration
node -e "
const now = new Date();
console.log('System Date:', now.toString());
console.log('UTC String:', now.toUTCString());
console.log('ISO String:', now.toISOString());
console.log('TZ:', process.env.TZ);
console.log('Timezone offset:', now.getTimezoneOffset(), 'minutes');
"
```

### **3. Database Test:**
```sql
-- Test database timezone
SELECT NOW() as current_time,
       timezone('UTC', NOW()) as utc_time,
       EXTRACT(timezone FROM NOW()) as tz_offset;

-- Test fixture dates
SELECT id, date,
       EXTRACT(timezone FROM date) as tz_offset,
       date AT TIME ZONE 'UTC' as utc_date
FROM fixtures
LIMIT 5;
```

### **4. API Test:**
```bash
# Test time-sensitive endpoint
curl "http://localhost:3000/football/fixtures/sync/daily"

# Expected response with UTC-safe time calculations
{
  "status": "Success",
  "stats": {
    "fixturesProcessed": 1062,
    "fixturesUpserted": 267,
    "duration": "3s"
  }
}
```

## 🚨 **Common Pitfalls & Solutions**

### **1. JavaScript Date Issues:**

#### **❌ Problem:**
```typescript
// Affected by system timezone
const now = new Date();
const diff = futureDate.getTime() - now.getTime();
```

#### **✅ Solution:**
```typescript
// UTC-safe
const now = this.utilsService.getUtcNow();
const diff = this.utilsService.getMinutesDifference(futureDate);
```

### **2. Date Parsing Issues:**

#### **❌ Problem:**
```typescript
// May parse incorrectly based on system timezone
const date = new Date(apiData.fixture.date);
```

#### **✅ Solution:**
```typescript
// Always parse to UTC
const date = this.utilsService.parseUtcDate(apiData.fixture.date);
```

### **3. Database Timezone Mismatch:**

#### **❌ Problem:**
```sql
-- Database in local timezone
timezone = 'America/New_York'
```

#### **✅ Solution:**
```sql
-- Database in UTC
timezone = 'UTC'
```

### **4. PM2 Production Issues:**

#### **❌ Problem:**
```javascript
// Missing timezone in production
{
  name: 'api',
  script: 'dist/main.js'
}
```

#### **✅ Solution:**
```javascript
// Explicit timezone setting
{
  name: 'api',
  script: 'dist/main.js',
  env: {
    TZ: 'UTC',
    NODE_TZ: 'UTC'
  }
}
```

## 📋 **Pre-deployment Checklist**

### **Environment Setup:**
- [ ] Set `TZ=UTC` in environment variables
- [ ] Set `NODE_TZ=UTC` for Node.js
- [ ] Configure database timezone to UTC
- [ ] Update PM2/Docker configuration

### **Code Verification:**
- [ ] Use `utilsService.getUtcNow()` instead of `new Date()`
- [ ] Use `utilsService.parseUtcDate()` for API dates
- [ ] Use `utilsService.getMinutesDifference()` for time calculations
- [ ] Verify cronjob `utcOffset: 0` settings

### **Testing:**
- [ ] Run UTC test script
- [ ] Verify database timezone queries
- [ ] Test time-sensitive endpoints
- [ ] Check log timestamps

### **Monitoring:**
- [ ] Monitor cronjob execution times
- [ ] Verify fixture time calculations
- [ ] Check for timezone-related errors

## 🎯 **Production Commands**

### **Start with UTC (Manual):**
```bash
# API Service
TZ=UTC NODE_TZ=UTC node dist/main.js

# Worker Service
TZ=UTC NODE_TZ=UTC node dist/worker.js
```

### **PM2 with UTC:**
```bash
# Start with ecosystem config
pm2 start ecosystem.config.js

# Or manual with env
pm2 start dist/main.js --name api --env TZ=UTC,NODE_TZ=UTC
pm2 start dist/worker.js --name worker --env TZ=UTC,NODE_TZ=UTC
```

### **Docker with UTC:**
```bash
# Run with timezone
docker run -e TZ=UTC -e NODE_TZ=UTC your-app

# Or in docker-compose.yml
environment:
  - TZ=UTC
  - NODE_TZ=UTC
```

## 🎊 **Kết luận**

✅ **UTC Configuration Complete:**
- Application scripts với `TZ=UTC`
- Cronjobs với `utcOffset: 0`
- Database columns với `timestamp with time zone`
- UTC helper functions trong UtilsService

✅ **Production Ready:**
- Environment variable setup
- Database timezone configuration
- System-level timezone settings
- Comprehensive testing procedures

✅ **Timezone-safe Code:**
- All time calculations use UTC helpers
- Date parsing always converts to UTC
- Time differences calculated safely
- Logging shows UTC timestamps

**Application bây giờ hoàn toàn timezone-independent và production-ready!** 🚀
