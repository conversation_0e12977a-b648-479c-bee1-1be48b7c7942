# League API Multiple Seasons Fix

## 🐛 **Vấn đề phát hiện**

Endpoint `GET /football/leagues?league=570` chỉ trả về 1 league trong DB, trong khi API external `https://v3.football.api-sports.io/leagues?id=570&last=2` trả về 1 league với 2 seasons.

### **Root Cause Analysis**

API Football trả về structure:
```json
{
  "response": [
    {
      "league": { "id": 570, "name": "Premier League" },
      "country": { "name": "Ghana" },
      "seasons": [
        { "year": 2024, "current": true },
        { "year": 2023, "current": false }
      ]
    }
  ]
}
```

**<PERSON> cũ** chỉ xử lý `seasons[0]` → chỉ lưu 1 season
**Logic mới** xử lý tất cả `seasons[]` → lưu tất cả seasons

## 🔧 **Thay đổi đã thực hiện**

### **1. Sửa fetchFromApi() trong LeagueService**

#### **Trước:**
```typescript
response.data.response.map(async (apiData: any) => {
    // Chỉ xử lý seasons[0]
    let league = await this.leagueRepository.findOneBy({
        externalId: apiData.league.id,
        season: apiData.seasons?.[0]?.year || 0,
    });
    
    if (!league) {
        league = this.leagueRepository.create({
            // ... other fields
            season: apiData.seasons?.[0]?.year || 0,
            season_detail: apiData.seasons?.[0] ? {
                year: apiData.seasons[0].year,
                // ...
            } : undefined,
        });
    }
    return league;
})
```

#### **Sau:**
```typescript
response.data.response.flatMap(async (apiData: any) => {
    // Xử lý TẤT CẢ seasons
    const seasons = apiData.seasons || [];
    
    const leaguePromises = seasons.map(async (seasonData: any) => {
        let league = await this.leagueRepository.findOneBy({
            externalId: apiData.league.id,
            season: seasonData.year || 0,
        });
        
        if (!league) {
            league = this.leagueRepository.create({
                // ... other fields
                season: seasonData.year || 0,
                active: seasonData.current || false,
                season_detail: {
                    year: seasonData.year,
                    start: seasonData.start,
                    end: seasonData.end,
                    current: seasonData.current,
                    coverage: seasonData.coverage,
                },
            });
        }
        return league;
    });
    
    return Promise.all(leaguePromises);
}).then(results => results.flat())
```

### **2. Sửa parameter mapping**

#### **Trước:**
```typescript
if (apiQuery.league) {
    apiQuery.id = apiQuery.league;
}
```

#### **Sau:**
```typescript
if (league) {
    apiQuery.id = league;
}
```

### **3. Tối ưu image download**

- Download league logo và country flag chỉ 1 lần per league
- Không download lại cho mỗi season

## ✅ **Kết quả kiểm tra**

### **API Test:**
```bash
# External API trả về 2 seasons
curl -H "x-apisports-key: xxx" \
  "https://v3.football.api-sports.io/leagues?id=570&last=2"

# Response: 1 league với 2 seasons (2024, 2023)
```

### **Local API Test:**
```bash
# Fetch từ API và lưu vào DB
curl "http://localhost:3000/football/leagues?league=570&newdb=true"

# Response: 4 leagues (4 seasons: 2024, 2023, 2022, 2011)
```

```bash
# Fetch từ DB
curl "http://localhost:3000/football/leagues?league=570"

# Response: 4 leagues từ database
```

## 📊 **So sánh trước và sau**

| Aspect | Trước | Sau |
|--------|-------|-----|
| **Seasons processed** | Chỉ seasons[0] | Tất cả seasons[] |
| **DB records** | 1 league/season | Multiple leagues/seasons |
| **Data completeness** | Thiếu data | Đầy đủ data |
| **API mapping** | Sai logic | Đúng logic |
| **Image download** | Per season | Per league (optimized) |

## 🎯 **Logic hoạt động mới**

### **1. Khi gọi API với newdb=true:**
```
1. Call external API với parameters
2. Nhận response với multiple seasons
3. Tạo separate league record cho mỗi season
4. Download images (logo, flag) một lần
5. Save tất cả league records vào DB
6. Return processed data
```

### **2. Khi gọi API không có newdb:**
```
1. Query database trước
2. Nếu có data → return từ DB
3. Nếu không có → call external API
4. Process và save như trên
```

### **3. Cache Strategy:**
```
- Cache key bao gồm league ID và season
- Clear cache khi có data mới từ API
- Cache timeout: 7 days
```

## 🔄 **Database Schema**

Mỗi season của cùng 1 league được lưu thành separate record:

```sql
-- League 570 bây giờ có 4 records
SELECT id, externalId, season, active, name 
FROM league 
WHERE externalId = 570;

-- Results:
-- id=55,   externalId=570, season=2011, active=false
-- id=1185, externalId=570, season=2024, active=true  
-- id=1186, externalId=570, season=2022, active=false
-- id=1187, externalId=570, season=2023, active=false
```

## 🎉 **Kết luận**

✅ **Vấn đề đã được sửa**: API bây giờ xử lý tất cả seasons từ external API
✅ **Data integrity**: Database có đầy đủ thông tin cho tất cả seasons
✅ **Performance**: Optimized image download
✅ **Consistency**: Logic xử lý nhất quán cho tất cả cases

Endpoint `GET /football/leagues?league=570` bây giờ trả về đúng số lượng seasons có trong database! 🎊
