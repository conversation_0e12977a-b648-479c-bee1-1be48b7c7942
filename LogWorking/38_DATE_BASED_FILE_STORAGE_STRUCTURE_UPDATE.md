# Date-Based File Storage Structure Update

## 🎯 **Overview**

Updated image upload system to organize files by date structure (YYYY/MM/DD) instead of category-based folders, following user requirement for better file organization.

## ❌ **Previous Structure**

### **Old File Organization:**
```
IMAGE_STORAGE_PATH/
├── leagues/
│   ├── premier-league-logo-1640995200000.png
│   └── la-liga-logo-1640995300000.jpg
├── teams/
│   ├── manchester-united-logo-1640995400000.png
│   └── barcelona-logo-1640995500000.jpg
├── flags/
│   └── england-flag-1640995600000.svg
└── venues/
    └── old-trafford-1640995700000.jpg
```

### **Issues with Old Structure:**
- Category-based organization
- Mixed upload dates in same folder
- Difficult to manage files by date
- No chronological organization

## ✅ **New Structure**

### **Updated File Organization:**
```
IMAGE_STORAGE_PATH/
└── 2025/
    ├── 05/
    │   ├── 24/
    │   │   ├── premier-league-logo-1640995200000.png
    │   │   ├── manchester-united-logo-1640995300000.jpg
    │   │   └── england-flag-1640995400000.svg
    │   └── 25/
    │       ├── barcelona-logo-1640995500000.jpg
    │       ├── la-liga-logo-1640995600000.png
    │       └── old-trafford-1640995700000.jpg
    └── 06/
        └── 01/
            └── new-uploads-here.png
```

### **Benefits of New Structure:**
- **Chronological Organization**: Files organized by upload date
- **Easy Maintenance**: Simple to find files by date
- **Automatic Cleanup**: Easy to implement date-based cleanup policies
- **Scalable**: Handles large volumes of files efficiently
- **Backup Friendly**: Date-based backup strategies

## 🔧 **Technical Implementation**

### **A. Helper Method for Date Path Creation:**
```typescript
private createDateBasedPath(): { dateFolder: string; fullDirectoryPath: string } {
    const now = new Date();
    const year = now.getFullYear().toString();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const dateFolder = path.join(year, month, day);
    const fullDirectoryPath = path.join(this.config.imageStoragePath, dateFolder);
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(fullDirectoryPath)) {
        fs.mkdirSync(fullDirectoryPath, { recursive: true });
    }
    
    return { dateFolder, fullDirectoryPath };
}
```

### **B. Updated File Upload Logic:**
```typescript
// File Upload Method
async uploadImageFromFile(file, category, uploadedBy, description) {
    // Generate unique filename
    const imageId = this.generateImageId();
    const fileExtension = path.extname(file.originalname);
    const sanitizedName = this.sanitizeFilename(file.originalname.replace(fileExtension, ''));
    const filename = `${sanitizedName}-${Date.now()}${fileExtension}`;
    
    // Create date-based directory structure: YYYY/MM/DD
    const { dateFolder, fullDirectoryPath } = this.createDateBasedPath();
    
    // Save file to: IMAGE_STORAGE_PATH/YYYY/MM/DD/filename
    const filePath = path.join(fullDirectoryPath, filename);
    fs.writeFileSync(filePath, file.buffer);
    
    // Generate public URL: /uploads/YYYY/MM/DD/filename
    const publicUrl = `${this.getBaseUrl()}/uploads/${dateFolder.replace(/\\/g, '/')}/${filename}`;
    
    // Save to database with date-based filename
    const uploadedImage = this.uploadedImageRepository.create({
        imageId,
        originalName: file.originalname,
        filename: `${dateFolder.replace(/\\/g, '/')}/${filename}`, // 2025/05/25/filename.png
        size: file.size,
        mimeType: file.mimetype,
        category, // Still stored for metadata
        path: filePath,
        url: publicUrl,
        description,
        uploadedBy,
    });
}
```

### **C. Updated URL Upload Logic:**
```typescript
// URL Upload Method
async uploadImageFromUrl(uploadDto, uploadedBy) {
    // Download image from URL
    const response = await axios({ url: uploadDto.imageUrl, ... });
    
    // Generate filename
    const imageId = this.generateImageId();
    const urlPath = new URL(uploadDto.imageUrl).pathname;
    const fileExtension = path.extname(urlPath) || this.getExtensionFromMimeType(contentType);
    const customFilename = uploadDto.filename ? this.sanitizeFilename(uploadDto.filename) : 'image';
    const filename = `${customFilename}-${Date.now()}${fileExtension}`;
    
    // Create date-based directory structure: YYYY/MM/DD
    const { dateFolder, fullDirectoryPath } = this.createDateBasedPath();
    
    // Save file to: IMAGE_STORAGE_PATH/YYYY/MM/DD/filename
    const filePath = path.join(fullDirectoryPath, filename);
    fs.writeFileSync(filePath, response.data);
    
    // Generate public URL: /uploads/YYYY/MM/DD/filename
    const publicUrl = `${this.getBaseUrl()}/uploads/${dateFolder.replace(/\\/g, '/')}/${filename}`;
}
```

## 📊 **Database Schema Updates**

### **Updated filename Field:**
```typescript
// Before: Category-based filename
filename: "leagues/premier-league-logo-1640995200000.png"

// After: Date-based filename  
filename: "2025/05/25/premier-league-logo-1640995200000.png"
```

### **Database Record Example:**
```json
{
  "id": 1,
  "imageId": "img_a1b2c3d4e5f6g7h8",
  "originalName": "premier-league-logo.png",
  "filename": "2025/05/25/premier-league-logo-1640995200000.png",
  "size": 15420,
  "mimeType": "image/png",
  "category": "leagues", // Still stored for metadata/filtering
  "path": "./public/images/2025/05/25/premier-league-logo-1640995200000.png",
  "url": "http://localhost:3000/uploads/2025/05/25/premier-league-logo-1640995200000.png",
  "description": "Premier League official logo",
  "uploadedBy": 1,
  "uploadedAt": "2025-05-25T10:30:00.000Z"
}
```

## 🌐 **Public URL Structure**

### **Updated URL Format:**
```
# Before: Category-based URLs
http://localhost:3000/uploads/leagues/premier-league-logo-1640995200000.png
http://localhost:3000/uploads/teams/manchester-united-logo-1640995300000.jpg

# After: Date-based URLs
http://localhost:3000/uploads/2025/05/25/premier-league-logo-1640995200000.png
http://localhost:3000/uploads/2025/05/25/manchester-united-logo-1640995300000.jpg
```

### **Static File Serving Configuration:**
```typescript
// main.ts - Static file serving remains the same
const imageStoragePath = configService.get<string>('IMAGE_STORAGE_PATH', './public/images');
app.useStaticAssets(join(process.cwd(), imageStoragePath), {
  prefix: '/uploads/',
});

// Now serves files from date-based structure automatically
```

## 📝 **Updated API Documentation**

### **Swagger Documentation Updates:**
```typescript
@ApiOperation({
    summary: 'Upload Image from File (SystemUser Only)',
    description: `
    **File Organization:**
    - Files stored in date-based structure: YYYY/MM/DD/filename
    - Categories used for metadata organization only
    - Automatic directory creation by upload date
    - Unique filename generation with timestamp

    **File Storage Structure:**
    IMAGE_STORAGE_PATH/
    └── 2025/
        └── 05/
            └── 25/
                ├── premier-league-logo-1640995200000.png
                ├── manchester-united-logo-1640995300000.jpg
                └── england-flag-1640995400000.svg
    `
})
```

### **Updated Response Examples:**
```json
{
  "id": "img_a1b2c3d4e5f6g7h8",
  "originalName": "premier-league-logo.png",
  "filename": "2025/05/25/premier-league-logo-1640995200000.png",
  "size": 15420,
  "mimeType": "image/png",
  "category": "leagues",
  "url": "http://localhost:3000/uploads/2025/05/25/premier-league-logo-1640995200000.png",
  "path": "./public/images/2025/05/25/premier-league-logo-1640995200000.png",
  "uploadedAt": "2025-05-25T10:30:00.000Z",
  "uploadedBy": 1,
  "description": "Premier League official logo"
}
```

## 🧪 **Testing Examples**

### **File Upload Test:**
```bash
# Upload file - will be stored in today's date folder
curl -X POST http://localhost:3000/upload/file \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -F "file=@premier-league-logo.png" \
  -F "category=leagues" \
  -F "description=Premier League logo"

# Response will show date-based filename and URL
{
  "filename": "2025/05/25/premier-league-logo-1640995200000.png",
  "url": "http://localhost:3000/uploads/2025/05/25/premier-league-logo-1640995200000.png"
}
```

### **URL Upload Test:**
```bash
# Upload from URL - will be stored in today's date folder
curl -X POST http://localhost:3000/upload/url \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://media.api-sports.io/football/leagues/39.png",
    "category": "leagues",
    "filename": "premier-league-logo"
  }'

# Response will show date-based structure
{
  "filename": "2025/05/25/premier-league-logo-1640995300000.png",
  "url": "http://localhost:3000/uploads/2025/05/25/premier-league-logo-1640995300000.png"
}
```

### **File Access Test:**
```bash
# Access uploaded file via date-based URL
curl http://localhost:3000/uploads/2025/05/25/premier-league-logo-1640995200000.png

# File will be served from: IMAGE_STORAGE_PATH/2025/05/25/premier-league-logo-1640995200000.png
```

## 🎯 **Benefits of New Structure**

### **✅ File Management:**
- **Chronological Organization**: Easy to find files by upload date
- **Automatic Directory Creation**: Directories created as needed
- **Scalable Structure**: Handles large volumes efficiently
- **Date-based Cleanup**: Easy to implement retention policies

### **✅ Operational Benefits:**
- **Backup Strategies**: Date-based backup policies
- **Storage Management**: Easy to archive old files
- **Performance**: Better file system performance with distributed folders
- **Maintenance**: Simple to clean up files by date range

### **✅ Developer Experience:**
- **Predictable URLs**: Date-based URL structure
- **Easy Debugging**: Find files by upload date
- **Clear Organization**: Logical file structure
- **Metadata Preserved**: Categories still available for filtering

### **✅ Production Ready:**
- **Environment Variable**: Uses IMAGE_STORAGE_PATH from .env
- **Cross-platform**: Works on Windows/Linux/macOS
- **Static Serving**: Automatic static file serving
- **Database Consistency**: Filename field updated accordingly

---

**Update Completed:** 2025-05-25
**Status:** ✅ Date-based file storage structure implemented
**File Organization:** IMAGE_STORAGE_PATH + YYYY/MM/DD/filename
**Build Status:** ✅ npm run build successful
**Backward Compatibility:** ✅ Categories preserved for metadata filtering
