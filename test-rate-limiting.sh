#!/bin/bash

# Test script for rate limiting
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************.iavND_ACfEGH6sl9Q6biWb5lAaLD-4ihA3zS9-h1QCg"

echo "Testing rate limiting with multiple concurrent requests..."

# Launch 5 concurrent requests
for i in {1..5}; do
    echo "Starting request $i..."
    curl -X POST http://localhost:3000/upload/url \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{\"imageUrl\": \"https://media.api-sports.io/football/teams/$((1000 + i)).png\", \"category\": \"teams\", \"filename\": \"rate-test-$i\", \"description\": \"Rate limiting test $i\"}" \
        > "response_$i.json" 2>&1 &
done

echo "Waiting for all requests to complete..."
wait

echo "Results:"
for i in {1..5}; do
    echo "Response $i:"
    cat "response_$i.json"
    echo "---"
done

# Cleanup
rm -f response_*.json
