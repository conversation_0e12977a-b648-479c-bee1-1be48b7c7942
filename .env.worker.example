# Worker Service Environment Variables

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=sports_game

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# External API Configuration
API_FOOTBALL_URL=https://v3.football.api-sports.io
API_FOOTBALL_KEY=your_api_key_here

# Sync Configuration
SYNC_BATCH_SIZE=100
MAX_IDS_PER_REQUEST=20
MAX_CONCURRENT_REQUESTS=1
LIVE_FIXTURES_INTERVAL=*/10 * * * * *

# API Rate Limiting
API_RATE_LIMIT=100

# Environment
NODE_ENV=development
