{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist"}, "include": ["src/worker.ts", "src/worker-sync.module.ts", "src/core/core-worker.module.ts", "src/core/config/**/*", "src/core/database/**/*", "src/core/cache/**/*", "src/core/logger/**/*", "src/core/constants/**/*", "src/core/index.ts", "src/shared/**/*", "src/sports/football/football.module.ts", "src/sports/football/football-worker.module.ts", "src/sports/football/sync.module.ts", "src/sports/football/sync.processor.ts", "src/sports/football/season-sync.module.ts", "src/sports/football/services/sync.service.ts", "src/sports/football/services/season-sync.service.ts", "src/sports/football/services/fixture.service.ts", "src/sports/football/services/league.service.ts", "src/sports/football/services/team.service.ts", "src/sports/football/services/fixture-statistics.service.ts", "src/sports/football/services/team-statistics.service.ts", "src/sports/football/models/**/*", "src/sports/football/index.ts"], "exclude": ["src/auth/**/*", "src/main.ts", "src/app.module.ts", "src/cms/**/*", "src/docs/**/*", "src/broadcast-links/**/*", "src/sports/football/controllers/**/*", "src/sports/football/football-api.module.ts", "test/**/*", "node_modules", "dist"]}